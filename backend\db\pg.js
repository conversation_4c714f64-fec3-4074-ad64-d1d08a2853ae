/**
 * PostgreSQL数据库连接模块
 * 提供数据库连接池实例
 */
const { Pool } = require('pg');

// 配置数据库连接参数
const connPG = new Pool({
    user: process.env.DB_USER,      // 替换为你的 PostgreSQL 用户名
    host: process.env.DB_HOST,      // 数据库服务器地址，默认是 localhost
    database: process.env.DB_DATABASE,  // 替换为你的数据库名称
    password: process.env.DB_PASSWORD,  // 替换为你的数据库密码
    port: process.env.DB_PORT,      // PostgreSQL 默认端口是 5432
    // 连接池配置
    max: 20,                        // 最大连接数
    idleTimeoutMillis: 30000,       // 连接最大空闲时间
    connectionTimeoutMillis: 10000,  // 连接超时时间
    // 设置客户端编码，确保正确处理中文字符
    options: '-c client_encoding=UTF8'
});

// 监听连接池事件
connPG.on('connect', async client => {
    console.log('Database connection established');

    // 设置客户端编码为UTF8，确保正确处理中文字符
    try {
        await client.query('SET client_encoding = \'UTF8\'');
        console.log('Client encoding set to UTF8');

        // 检查客户端编码设置
        const result = await client.query('SHOW client_encoding');
        console.log('Current client encoding:', result.rows[0].client_encoding);
    } catch (error) {
        console.error('Error setting client encoding:', error);
    }
});

connPG.on('error', (err, client) => {
    console.error('Unexpected error on idle client', err);
});

module.exports = {
    connPG
};
