-- ----------------------------
-- View structure for v_cmdb_user_logs
-- ----------------------------
DROP VIEW IF EXISTS "public"."v_cmdb_user_logs";
CREATE VIEW "public"."v_cmdb_user_logs" AS  SELECT ((t.username::text ||
        CASE
            WHEN t.operation_type::text = 'add'::text THEN '创建了新的'::text
            WHEN t.operation_type::text = 'update'::text THEN '更新了'::text
            WHEN t.operation_type::text = 'delete'::text THEN '删除了'::text
            ELSE '其它'::text
        END) || t2.table_comment) || '数据'::text AS activity_info,
    to_char(t."timestamp", 'yyyy-mm-dd hh24:mi:ss'::text) AS activity_time,
        CASE
            WHEN t.operation_type::text = 'add'::text THEN 'primary'::text
            WHEN t.operation_type::text = 'update'::text THEN 'success'::text
            WHEN t.operation_type::text = 'delete'::text THEN 'warning'::text
            ELSE 'primary'::text
        END AS activity_type
   FROM cmdb_user_logs t
     JOIN ( SELECT n.nspname,
            c.relname AS table_name,
            pgd.description AS table_comment
           FROM pg_class c
             JOIN pg_namespace n ON n.oid = c.relnamespace
             JOIN pg_description pgd ON pgd.objoid = c.oid AND pgd.objsubid = 0
          WHERE c.relkind = 'r'::"char" AND n.nspname = 'public'::name) t2 ON t2.table_name = replace(regexp_replace(t.url, '/api/(del_|update_|add_)'::text, ''::text, 'g'::text), '_password'::text, ''::text)
  ORDER BY t."timestamp" DESC, t.id
 LIMIT 5;
