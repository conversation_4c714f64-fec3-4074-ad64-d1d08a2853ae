# CMDB系统后端性能及安全优化规划方案

## 一、概述

本文档针对CMDB系统后端进行全面的性能及安全优化分析，提出具体的优化方案和实施路径。优化目标是提高系统响应速度、降低资源消耗、增强系统稳定性和安全性。

## 二、当前系统分析

### 2.1 系统架构

当前CMDB系统后端基于Node.js和Express框架开发，使用PostgreSQL作为数据库，主要功能包括：
- 资产管理（服务器、网络设备、虚拟机等）
- 自动发现功能（网络扫描、设备发现）
- 调度任务管理
- 用户权限管理

### 2.2 性能瓶颈

通过代码分析，发现以下性能瓶颈：
1. 数据库查询效率低下
2. 网络扫描过程资源消耗大
3. 任务队列管理机制简单
4. 内存管理不完善
5. 日志处理影响性能

### 2.3 安全隐患

存在的主要安全隐患：
1. SQL注入风险
2. 输入验证不完善
3. 敏感信息保护不足
4. 缺乏完善的权限控制
5. 缺少API访问限制

## 三、优化方案

### 3.1 数据库优化

#### 3.1.1 索引优化
- **高优先级**
  - 为`cmdb_discovery_tasks`表的`task_name`、`task_type`、`status`字段添加索引
  - 为`cmdb_discovery_results`表的`task_id`、`ip_address`字段添加索引
  - 为所有表的`del_flag`字段创建部分索引（只索引`del_flag = '0'`的记录）

- **实施步骤**
  ```sql
  -- 为discovery_tasks表添加索引
  CREATE INDEX idx_discovery_tasks_name ON cmdb_discovery_tasks(task_name) WHERE del_flag = '0';
  CREATE INDEX idx_discovery_tasks_type ON cmdb_discovery_tasks(task_type) WHERE del_flag = '0';
  CREATE INDEX idx_discovery_tasks_status ON cmdb_discovery_tasks(status) WHERE del_flag = '0';

  -- 为discovery_results表添加索引
  CREATE INDEX idx_discovery_results_task_id ON cmdb_discovery_results(task_id) WHERE del_flag = '0';
  CREATE INDEX idx_discovery_results_ip ON cmdb_discovery_results(ip_address) WHERE del_flag = '0';
  ```

#### 3.1.2 查询语句优化
- **中优先级**
  - 优化`getDiscoveryTasks`函数中的子查询
  - 减少`getScheduleTasks`函数中的日志输出
  - 使用视图替代复杂查询

- **实施示例**
  ```javascript
  // 优化前
  const sqlStr = `
      SELECT
          t.id, t.task_name, t.task_type, t.description, t.ip_range_start, t.ip_range_end,
          t.ip_cidr, t.ip_range_type, t.scan_ports, t.schedule_type, t.schedule_value, t.last_run_time,
          t.status, t.created_at, t.created_by, t.updated_at, t.updated_by, t.run_duration_seconds,
          (SELECT COUNT(*) FROM cmdb_discovery_results r WHERE r.task_id = t.id AND r.del_flag = '0') AS result_count
      FROM
          cmdb_discovery_tasks t
      WHERE
          t.del_flag = '0'
  `;

  // 优化后
  const sqlStr = `
      SELECT
          t.*, r.result_count
      FROM
          cmdb_discovery_tasks t
      LEFT JOIN (
          SELECT task_id, COUNT(*) AS result_count
          FROM cmdb_discovery_results
          WHERE del_flag = '0'
          GROUP BY task_id
      ) r ON t.id = r.task_id
      WHERE
          t.del_flag = '0'
  `;
  ```

#### 3.1.3 连接池优化
- **高优先级**
  - 配置适当的连接池参数
  - 实现连接池监控

- **实施示例**
  ```javascript
  const connPG = new Pool({
      user: process.env.DB_USER,
      host: process.env.DB_HOST,
      database: process.env.DB_DATABASE,
      password: process.env.DB_PASSWORD,
      port: process.env.DB_PORT,
      // 添加以下配置
      max: 20,                       // 最大连接数
      idleTimeoutMillis: 30000,      // 连接最大空闲时间
      connectionTimeoutMillis: 10000, // 连接超时时间
  });

  // 添加连接池监控
  connPG.on('connect', client => {
      console.log('Database connection established');
  });

  connPG.on('error', (err, client) => {
      console.error('Unexpected error on idle client', err);
  });
  ```

#### 3.1.4 分页查询优化
- **中优先级**
  - 使用键集分页替代OFFSET分页

- **实施示例**
  ```javascript
  // 优化前 - 使用OFFSET分页
  const sqlStr = `
      SELECT * FROM cmdb_discovery_tasks
      WHERE del_flag = '0'
      ORDER BY id DESC
      LIMIT ${perPage} OFFSET ${(page - 1) * perPage}
  `;

  // 优化后 - 使用键集分页
  const sqlStr = `
      SELECT * FROM cmdb_discovery_tasks
      WHERE del_flag = '0' AND id < ${lastId}
      ORDER BY id DESC
      LIMIT ${perPage}
  `;
  ```

### 3.2 网络扫描优化

#### 3.2.1 并发控制优化
- **高优先级**
  - 优化`scanNetwork`函数中的并发控制
  - 实现自适应的批处理大小

- **实施示例**
  ```javascript
  // 优化前
  const BATCH_SIZE = 20; // 固定批处理大小

  // 优化后
  // 根据系统资源动态调整批处理大小
  const getBatchSize = () => {
      const freeMem = os.freemem();
      const totalMem = os.totalmem();
      const memoryUsage = 1 - (freeMem / totalMem);

      // 根据内存使用率调整批处理大小
      if (memoryUsage > 0.8) return 10;
      if (memoryUsage > 0.6) return 20;
      return 30;
  };
  ```

#### 3.2.2 扫描策略优化
- **中优先级**
  - 使用异步TCP连接检测替代ping命令
  - 优化端口扫描策略

- **实施示例**
  ```javascript
  // 优化前 - 使用ping命令
  function pingHost(ip, timeout) {
      return new Promise((resolve) => {
          const pingCmd = `ping -n 1 -w ${timeout} ${ip}`;
          exec(pingCmd, (error) => {
              resolve(!error);
          });
      });
  }

  // 优化后 - 使用TCP连接检测
  function checkHostAlive(ip, timeout) {
      return new Promise((resolve) => {
          const socket = new net.Socket();
          socket.setTimeout(timeout);

          socket.on('connect', () => {
              socket.destroy();
              resolve(true);
          });

          socket.on('timeout', () => {
              socket.destroy();
              resolve(false);
          });

          socket.on('error', () => {
              socket.destroy();
              resolve(false);
          });

          // 尝试连接常用端口
          socket.connect(80, ip);
      });
  }
  ```

#### 3.2.3 结果缓存
- **中优先级**
  - 实现扫描结果缓存机制
  - 设置合理的缓存过期时间

- **实施示例**
  ```javascript
  // 引入缓存模块
  const NodeCache = require('node-cache');
  const scanResultCache = new NodeCache({ stdTTL: 300 }); // 5分钟过期

  // 在扫描前检查缓存
  async function scanDeviceWithCache(ip, timeout, ports) {
      const cacheKey = `scan_${ip}_${ports.join('_')}`;
      const cachedResult = scanResultCache.get(cacheKey);

      if (cachedResult) {
          return cachedResult;
      }

      // 执行实际扫描
      const result = await scanDevice(ip, timeout, ports);

      // 缓存结果
      if (result) {
          scanResultCache.set(cacheKey, result);
      }

      return result;
  }
  ```

### 3.3 任务队列优化

#### 3.3.1 队列管理优化
- **高优先级**
  - 使用专业队列管理系统替代内存队列
  - 实现事件驱动的队列处理
  - 支持任务持久化和故障恢复
  - 提供任务优先级和延迟执行功能

- **实施方案**
  1. 引入Bull队列库（基于Redis）
  2. 重构任务队列处理逻辑
  3. 实现队列监控和管理接口
  4. 添加任务重试和错误处理机制

- **实施示例**
  ```javascript
  // 引入Bull队列
  const Queue = require('bull');

  // 创建发现任务队列
  const discoveryQueue = new Queue('discovery-tasks', {
      redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || 6379,
          password: process.env.REDIS_PASSWORD
      },
      defaultJobOptions: {
          attempts: 3,
          backoff: {
              type: 'exponential',
              delay: 1000
          },
          removeOnComplete: 100,  // 保留最近100个完成的任务
          removeOnFail: 200       // 保留最近200个失败的任务
      }
  });

  // 添加任务到队列
  async function addTaskToQueue(taskId, taskData, priority = 'normal') {
      // 支持任务优先级
      const options = {
          priority: priority === 'high' ? 1 : priority === 'low' ? 3 : 2,
          jobId: `task_${taskId}`,  // 确保任务ID唯一
          attempts: taskData.critical ? 5 : 3  // 关键任务增加重试次数
      };

      // 如果是延迟任务，添加延迟选项
      if (taskData.delayMs) {
          options.delay = taskData.delayMs;
      }

      await discoveryQueue.add(
          { taskId, ...taskData },
          options
      );
  }

  // 处理队列任务
  discoveryQueue.process(MAX_CONCURRENT_TASKS, async (job) => {
      const { taskId, req } = job.data;

      // 更新任务状态为运行中
      await updateTaskStatus(taskId, 'running');

      try {
          // 记录任务开始时间
          const startTime = Date.now();

          // 执行任务
          const result = await runDiscoveryTask(req);

          // 记录任务执行时间
          const duration = Date.now() - startTime;
          await updateTaskDuration(taskId, duration);

          // 更新任务状态为完成
          await updateTaskStatus(taskId, 'completed');

          return result;
      } catch (error) {
          console.error(`任务${taskId}执行失败:`, error);

          // 更新任务状态为失败
          await updateTaskStatus(taskId, 'failed', error.message);

          throw error;
      }
  });

  // 监听任务完成事件
  discoveryQueue.on('completed', (job) => {
      console.log(`任务${job.data.taskId}已完成`);
  });

  // 监听任务失败事件
  discoveryQueue.on('failed', (job, error) => {
      console.error(`任务${job.data.taskId}失败:`, error);
  });
  ```

#### 3.3.2 并发限制优化
- **中优先级**
  - 实现动态并发限制
  - 添加任务优先级支持

- **实施示例**
  ```javascript
  // 动态调整并发数
  function getMaxConcurrentTasks() {
      const cpuUsage = os.loadavg()[0] / os.cpus().length;

      // 根据CPU负载调整并发数
      if (cpuUsage > 0.8) return 2;
      if (cpuUsage > 0.5) return 3;
      return 5;
  }

  // 定期更新并发限制
  setInterval(() => {
      const newLimit = getMaxConcurrentTasks();
      discoveryQueue.concurrency = newLimit;
      console.log(`已将任务并发数调整为: ${newLimit}`);
  }, 60000); // 每分钟检查一次
  ```

### 3.4 缓存优化

#### 3.4.1 数据字典缓存
- **高优先级**
  - 使用LRU缓存替代简单对象缓存
  - 添加缓存大小限制
  - 实现按类型分组缓存

- **现状分析**
  - 已经实现了简单的内存缓存（见index.js中的dictionaryCache）
  - 缓存过期时间设置为5分钟
  - 缺乏大小限制和高效的缓存淘汰策略

- **实施示例**
  ```javascript
  const LRU = require('lru-cache');

  // 创建数据字典缓存
  const dictionaryCache = new LRU({
      max: 1000,           // 最多存储1000个条目
      maxAge: 1000 * 60 * 5 // 5分钟过期
  });

  // 获取数据字典的函数
  async function getDictionary(dictCode, forceRefresh = false) {
      const cacheKey = dictCode || 'all';

      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && dictionaryCache.has(cacheKey)) {
          return dictionaryCache.get(cacheKey);
      }

      // 从数据库获取数据
      const result = await fetchDictionaryFromDB(dictCode);

      // 更新缓存
      dictionaryCache.set(cacheKey, result);

      return result;
  }
  ```

#### 3.4.2 用户权限缓存
- **高优先级**
  - 实现用户权限缓存
  - 设置合理的过期时间
  - 在权限变更时主动清除缓存

- **现状分析**
  - 每次请求都从数据库查询用户权限
  - 登录时获取权限信息较慢
  - 权限数据变更不频繁但查询频繁

- **实施示例**
  ```javascript
  const NodeCache = require('node-cache');
  const permissionCache = new NodeCache({ stdTTL: 1800 }); // 30分钟过期

  // 获取用户权限的函数
  async function getUserPermissions(userId, forceRefresh = false) {
      const cacheKey = `perm_${userId}`;

      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && permissionCache.has(cacheKey)) {
          return permissionCache.get(cacheKey);
      }

      // 从数据库查询权限
      const permissionQuery = `
          SELECT p.page_code, p.page_path
          FROM cmdb_pages p
          JOIN cmdb_user_page_permissions upp ON p.id = upp.page_id
          WHERE upp.user_id = $1
          AND p.del_flag = '0'
          AND upp.del_flag = '0'
      `;

      const permissionResult = await connPG.query(permissionQuery, [userId]);
      const permissions = permissionResult.rows;

      // 更新缓存
      permissionCache.set(cacheKey, permissions);

      return permissions;
  }

  // 清除用户权限缓存
  function clearUserPermissionCache(userId) {
      const cacheKey = `perm_${userId}`;
      permissionCache.del(cacheKey);
  }
  ```

#### 3.4.3 网络扫描结果缓存
- **中优先级**
  - 实现扫描结果缓存
  - 设置合理的过期时间（如5-15分钟）
  - 提供强制刷新选项

- **现状分析**
  - 每次扫描都执行完整的网络探测
  - 短时间内重复扫描相同IP浪费资源
  - 扫描操作耗时且消耗网络资源

- **实施示例**
  ```javascript
  const NodeCache = require('node-cache');
  const scanResultCache = new NodeCache({ stdTTL: 900 }); // 15分钟过期

  // 带缓存的设备扫描函数
  async function scanDeviceWithCache(ip, timeout, ports, forceRefresh = false) {
      const cacheKey = `scan_${ip}_${ports.join('_')}`;

      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && scanResultCache.has(cacheKey)) {
          return scanResultCache.get(cacheKey);
      }

      // 执行实际扫描
      const result = await scanDevice(ip, timeout, ports);

      // 缓存结果
      if (result) {
          scanResultCache.set(cacheKey, result);
      }

      return result;
  }
  ```

#### 3.4.4 任务状态缓存
- **中优先级**
  - 实现任务状态缓存
  - 设置较短的过期时间（如1分钟）
  - 在任务状态变更时主动更新缓存

- **现状分析**
  - 频繁查询任务状态
  - 每次查询都访问数据库
  - 任务状态查询是高频操作

- **实施示例**
  ```javascript
  const LRU = require('lru-cache');

  // 创建任务状态缓存
  const taskStatusCache = new LRU({
      max: 1000,           // 最多存储1000个任务状态
      maxAge: 1000 * 60    // 1分钟过期
  });

  // 获取任务状态的函数
  async function getTaskStatus(taskId, forceRefresh = false) {
      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && taskStatusCache.has(taskId)) {
          return taskStatusCache.get(taskId);
      }

      // 从数据库查询任务状态
      const query = `SELECT status FROM cmdb_discovery_tasks WHERE id = $1`;
      const result = await connPG.query(query, [taskId]);

      if (result.rows.length === 0) {
          return null;
      }

      const status = result.rows[0].status;

      // 更新缓存
      taskStatusCache.set(taskId, status);

      return status;
  }

  // 更新任务状态时同时更新缓存
  async function updateTaskStatus(taskId, newStatus) {
      // 更新数据库
      const query = `UPDATE cmdb_discovery_tasks SET status = $1 WHERE id = $2`;
      await connPG.query(query, [newStatus, taskId]);

      // 更新缓存
      taskStatusCache.set(taskId, newStatus);
  }
  ```

#### 3.4.5 资产信息缓存
- **中优先级**
  - 实现资产信息缓存
  - 按资产类型分组缓存
  - 设置较长的过期时间（如30分钟）
  - 在资产变更时主动清除缓存

- **现状分析**
  - 频繁查询资产信息
  - 资产数据变更不频繁但查询频繁
  - 资产信息查询涉及多表关联

- **实施示例**
  ```javascript
  const NodeCache = require('node-cache');
  const assetCache = new NodeCache({ stdTTL: 1800 }); // 30分钟过期

  // 获取资产信息的函数
  async function getAssetInfo(assetId, assetType, forceRefresh = false) {
      const cacheKey = `${assetType}_${assetId}`;

      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && assetCache.has(cacheKey)) {
          return assetCache.get(cacheKey);
      }

      // 根据资产类型构建查询
      let query;
      switch (assetType) {
          case 'server':
              query = `SELECT * FROM cmdb_server_management WHERE id = $1 AND del_flag = '0'`;
              break;
          case 'network':
              query = `SELECT * FROM cmdb_device_management WHERE id = $1 AND del_flag = '0'`;
              break;
          case 'vm':
              query = `SELECT * FROM cmdb_vm_registry WHERE id = $1 AND del_flag = '0'`;
              break;
          default:
              throw new Error('未知的资产类型');
      }

      // 执行查询
      const result = await connPG.query(query, [assetId]);

      if (result.rows.length === 0) {
          return null;
      }

      const assetInfo = result.rows[0];

      // 更新缓存
      assetCache.set(cacheKey, assetInfo);

      return assetInfo;
  }

  // 清除资产缓存
  function clearAssetCache(assetId, assetType) {
      const cacheKey = `${assetType}_${assetId}`;
      assetCache.del(cacheKey);
  }
  ```

#### 3.4.6 查询结果缓存
- **中优先级**
  - 实现查询结果缓存
  - 根据查询参数生成缓存键
  - 设置适当的过期时间（如5分钟）
  - 对于分页查询，缓存总数和首页结果

- **现状分析**
  - 相同的查询条件重复执行
  - 分页查询每次都访问数据库
  - 复杂查询耗时且消耗数据库资源

- **实施示例**
  ```javascript
  const LRU = require('lru-cache');

  // 创建查询结果缓存
  const queryResultCache = new LRU({
      max: 500,            // 最多存储500个查询结果
      maxAge: 1000 * 60 * 5 // 5分钟过期
  });

  // 生成缓存键
  function generateCacheKey(tableName, params) {
      return `${tableName}_${JSON.stringify(params)}`;
  }

  // 带缓存的查询函数
  async function queryWithCache(tableName, params, forceRefresh = false) {
      const cacheKey = generateCacheKey(tableName, params);

      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && queryResultCache.has(cacheKey)) {
          return queryResultCache.get(cacheKey);
      }

      // 执行实际查询
      const result = await executeQuery(tableName, params);

      // 缓存结果
      queryResultCache.set(cacheKey, result);

      return result;
  }

  // 清除特定表的所有缓存
  function clearTableCache(tableName) {
      // 获取所有缓存键
      const keys = queryResultCache.keys();

      // 删除匹配的缓存
      for (const key of keys) {
          if (key.startsWith(`${tableName}_`)) {
              queryResultCache.del(key);
          }
      }
  }
  ```

#### 3.4.7 配置信息缓存
- **高优先级**
  - 实现配置信息缓存
  - 设置较长的过期时间（如1小时）
  - 在配置变更时主动更新缓存

- **现状分析**
  - 系统配置信息频繁读取
  - 配置变更不频繁
  - 配置信息是系统运行的基础数据

- **实施示例**
  ```javascript
  const NodeCache = require('node-cache');
  const configCache = new NodeCache({ stdTTL: 3600 }); // 1小时过期

  // 获取系统配置的函数
  async function getSystemConfig(configKey, forceRefresh = false) {
      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && configCache.has(configKey)) {
          return configCache.get(configKey);
      }

      // 从数据库查询配置
      const query = `SELECT config_value FROM cmdb_system_config WHERE config_key = $1`;
      const result = await connPG.query(query, [configKey]);

      if (result.rows.length === 0) {
          return null;
      }

      const configValue = result.rows[0].config_value;

      // 更新缓存
      configCache.set(configKey, configValue);

      return configValue;
  }

  // 更新系统配置时同时更新缓存
  async function updateSystemConfig(configKey, configValue, username) {
      // 更新数据库
      const query = `
          UPDATE cmdb_system_config SET
              config_value = $1,
              updated_at = NOW(),
              updated_by = $2
          WHERE config_key = $3
      `;

      await connPG.query(query, [configValue, username, configKey]);

      // 更新缓存
      configCache.set(configKey, configValue);
  }
  ```

#### 3.4.8 仪表盘数据缓存
- **中优先级**
  - 实现仪表盘数据缓存
  - 设置适当的过期时间（如5分钟）
  - 提供强制刷新选项

- **现状分析**
  - 仪表盘数据查询复杂且耗时
  - 数据变化不频繁但访问频繁
  - 仪表盘是高频访问页面

- **实施示例**
  ```javascript
  const NodeCache = require('node-cache');
  const dashboardCache = new NodeCache({ stdTTL: 300 }); // 5分钟过期

  // 获取仪表盘数据的函数
  async function getDashboardData(forceRefresh = false) {
      const cacheKey = 'dashboard_data';

      // 如果不强制刷新且缓存中有数据，则使用缓存
      if (!forceRefresh && dashboardCache.has(cacheKey)) {
          return dashboardCache.get(cacheKey);
      }

      // 查询仪表盘数据
      const sqlStr = `
          SELECT asset_count,
                 asset_growth,
                 config_count,
                 config_growth
          FROM v_cmdb_dashboard
      `;

      try {
          const result = await connPG.query(sqlStr);

          // 缓存结果
          dashboardCache.set(cacheKey, result.rows);

          return result.rows;
      } catch (error) {
          console.error('获取仪表盘数据失败:', error);
          throw error;
      }
  }

  // 清除仪表盘缓存
  function clearDashboardCache() {
      dashboardCache.del('dashboard_data');
  }
  ```

### 3.5 内存管理优化

#### 3.5.1 大对象处理
- **中优先级**
  - 实现流式处理大数据集
  - 分批处理大量结果

- **实施示例**
  ```javascript
  // 分批处理大量扫描结果
  async function processScanResults(discoveredDevices) {
      const BATCH_SIZE = 100;
      const batches = [];

      // 将结果分成多个批次
      for (let i = 0; i < discoveredDevices.length; i += BATCH_SIZE) {
          batches.push(discoveredDevices.slice(i, i + BATCH_SIZE));
      }

      // 逐批处理
      for (const batch of batches) {
          await processBatch(batch);
          // 给GC一些时间回收内存
          await new Promise(resolve => setTimeout(resolve, 100));
      }
  }
  ```

### 3.5 日志优化

#### 3.5.1 日志系统优化
- **中优先级**
  - 引入专业日志库
  - 实现日志级别控制
  - 配置异步日志写入

- **实施示例**
  ```javascript
  const winston = require('winston');

  // 配置日志系统
  const logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
      ),
      transports: [
          // 控制台输出
          new winston.transports.Console(),
          // 文件输出
          new winston.transports.File({
              filename: 'error.log',
              level: 'error',
              maxsize: 5242880, // 5MB
              maxFiles: 5
          }),
          new winston.transports.File({
              filename: 'combined.log',
              maxsize: 10485760, // 10MB
              maxFiles: 5
          })
      ]
  });

  // 替换console.log等调用
  // 例如: console.log('任务执行完成') -> logger.info('任务执行完成')
  ```

#### 3.5.2 请求日志优化
- **低优先级**
  - 优化请求日志中间件
  - 实现日志采样

- **实施示例**
  ```javascript
  // 优化请求日志中间件
  function optimizedLogRequests(req, res, next) {
      // 跳过静态资源和健康检查
      if (req.path.startsWith('/static') || req.path === '/health') {
          return next();
      }

      // 日志采样 - 只记录10%的常规请求
      if (req.method === 'GET' && Math.random() > 0.1) {
          return next();
      }

      // 记录请求信息
      const start = Date.now();

      // 响应完成后记录
      res.on('finish', () => {
          const duration = Date.now() - start;
          logger.info({
              method: req.method,
              path: req.path,
              status: res.statusCode,
              duration: `${duration}ms`
          });
      });

      next();
  }
  ```

### 3.6 安全优化

#### 3.6.1 SQL注入防护
- **高优先级**
  - 修复所有直接拼接SQL的地方
  - 实现SQL参数白名单验证

- **实施示例**
  ```javascript
  // 优化前 - 直接拼接SQL
  const sqlStr = `
      SELECT * FROM cmdb_discovery_tasks
      ORDER BY ${sortProp} ${sortOrder.toUpperCase()}
  `;

  // 优化后 - 使用白名单验证
  function validateSortField(field) {
      const allowedFields = ['id', 'task_name', 'created_at', 'status'];
      return allowedFields.includes(field) ? field : 'id';
  }

  function validateSortOrder(order) {
      return ['ASC', 'DESC'].includes(order.toUpperCase()) ? order.toUpperCase() : 'DESC';
  }

  const validatedSortProp = validateSortField(sortProp);
  const validatedSortOrder = validateSortOrder(sortOrder);

  const sqlStr = `
      SELECT * FROM cmdb_discovery_tasks
      ORDER BY "${validatedSortProp}" ${validatedSortOrder}
  `;
  ```

#### 3.6.2 输入验证
- **高优先级**
  - 引入输入验证库
  - 为所有API添加输入验证

- **实施示例**
  ```javascript
  const { body, validationResult } = require('express-validator');

  // 添加任务验证规则
  const taskValidationRules = [
      body('task_name').isString().trim().isLength({ min: 1, max: 100 }),
      body('task_type').isIn(['ping', 'port_scan', 'full_scan']),
      body('ip_range_start').optional().isIP(),
      body('ip_range_end').optional().isIP(),
      body('ip_cidr').optional().matches(/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/)
  ];

  // 验证中间件
  function validate(req, res, next) {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
          return res.status(400).json({ code: 1, msg: '输入验证失败', errors: errors.array() });
      }
      next();
  }

  // 应用到路由
  router.post('/add_discovery_task', taskValidationRules, validate, async (req, res) => {
      // 处理请求...
  });
  ```

#### 3.6.3 敏感信息保护
- **高优先级**
  - 加密存储敏感信息
  - 实现日志脱敏

- **实施示例**
  ```javascript
  // 日志脱敏
  function sanitizeLogData(data) {
      const sensitiveFields = ['password', 'token', 'secret'];
      const sanitized = { ...data };

      for (const field of sensitiveFields) {
          if (sanitized[field]) {
              sanitized[field] = '******';
          }
      }

      return sanitized;
  }

  // 使用脱敏函数
  logger.info(sanitizeLogData(requestData));
  ```

#### 3.6.4 API访问限制
- **中优先级**
  - 实现请求速率限制
  - 添加API访问日志

- **实施示例**
  ```javascript
  const rateLimit = require('express-rate-limit');

  // 创建限速中间件
  const apiLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 每个IP最多100个请求
      standardHeaders: true,
      legacyHeaders: false,
      message: { code: 1, msg: '请求过于频繁，请稍后再试' }
  });

  // 应用到所有API路由
  app.use('/api', apiLimiter);

  // 为敏感操作设置更严格的限制
  const sensitiveApiLimiter = rateLimit({
      windowMs: 60 * 60 * 1000, // 1小时
      max: 10, // 每个IP最多10个请求
      message: { code: 1, msg: '敏感操作请求过于频繁，请稍后再试' }
  });

  // 应用到敏感路由
  app.use('/api/user', sensitiveApiLimiter);
  ```

## 四、实施计划

### 4.1 优先级划分

#### 第一阶段（高优先级）
1. 数据库索引优化
2. 连接池配置优化
3. 任务队列管理优化（引入Bull队列）
4. SQL注入防护
5. 输入验证实现
6. 关键缓存实现：
   - 数据字典缓存
   - 用户权限缓存
   - 配置信息缓存

#### 第二阶段（中优先级）
1. 查询语句优化
2. 网络扫描策略优化
3. 并发限制优化
4. 日志系统优化
5. API访问限制
6. 次要缓存实现：
   - 任务状态缓存
   - 资产信息缓存
   - 查询结果缓存
   - 仪表盘数据缓存

#### 第三阶段（低优先级）
1. 分页查询优化
2. 网络扫描结果缓存
3. 请求日志优化
4. 代码结构重构
5. 缓存监控与优化

### 4.2 时间规划

| 阶段 | 任务 | 预计工时 | 负责人 |
|------|------|----------|--------|
| 第一阶段 | 数据库索引优化 | 2人天 | 待定 |
| 第一阶段 | 连接池配置优化 | 1人天 | 待定 |
| 第一阶段 | 任务队列管理优化 | 5人天 | 待定 |
| 第一阶段 | SQL注入防护 | 3人天 | 待定 |
| 第一阶段 | 输入验证实现 | 4人天 | 待定 |
| 第二阶段 | 查询语句优化 | 3人天 | 待定 |
| 第二阶段 | 网络扫描策略优化 | 4人天 | 待定 |
| 第二阶段 | 并发限制优化 | 2人天 | 待定 |
| 第二阶段 | 日志系统优化 | 3人天 | 待定 |
| 第二阶段 | API访问限制 | 2人天 | 待定 |
| 第三阶段 | 分页查询优化 | 2人天 | 待定 |
| 第三阶段 | 结果缓存实现 | 3人天 | 待定 |
| 第三阶段 | 请求日志优化 | 1人天 | 待定 |
| 第三阶段 | 代码结构重构 | 5人天 | 待定 |
| 第一阶段 | 数据字典缓存 | 2人天 | 待定 |
| 第一阶段 | 用户权限缓存 | 2人天 | 待定 |
| 第一阶段 | 配置信息缓存 | 1人天 | 待定 |
| 第二阶段 | 任务状态缓存 | 2人天 | 待定 |
| 第二阶段 | 资产信息缓存 | 3人天 | 待定 |
| 第二阶段 | 查询结果缓存 | 3人天 | 待定 |
| 第二阶段 | 仪表盘数据缓存 | 2人天 | 待定 |
| 第三阶段 | 网络扫描结果缓存 | 2人天 | 待定 |
| 第三阶段 | 缓存监控与优化 | 3人天 | 待定 |

### 4.3 缓存实施策略

#### 4.3.1 缓存库选择
- **简单场景**：使用Node.js内置的Map或Object
  - 适用于：临时缓存、单进程应用、小型数据集
  - 优点：无需额外依赖、实现简单、性能高
  - 缺点：不支持过期策略、无法跨进程共享

- **中等复杂度**：使用node-cache或lru-cache
  - 适用于：需要过期策略、需要LRU淘汰、中等数据量
  - 优点：实现简单、支持过期和淘汰策略、性能好
  - 缺点：不支持持久化、不支持分布式

- **分布式场景**：使用Redis（通过ioredis或redis客户端）
  - 适用于：多进程/多服务器共享、大数据量、需要持久化
  - 优点：支持分布式、持久化、丰富的数据结构
  - 缺点：需要额外维护Redis服务、增加系统复杂性

#### 4.3.2 缓存策略
- **读取时缓存（Lazy Loading）**
  - 策略：首次访问时加载并缓存
  - 优点：只缓存实际需要的数据、实现简单
  - 缺点：首次访问较慢、可能出现缓存穿透

- **写入时更新（Write-Through）**
  - 策略：数据变更时同步更新缓存
  - 优点：缓存始终保持最新、避免脏读
  - 缺点：写入操作变慢、可能缓存无用数据

- **定时刷新（Time-Based）**
  - 策略：定期刷新热点数据
  - 优点：保持热点数据最新、减少突发负载
  - 缺点：增加系统复杂性、可能浪费资源

#### 4.3.3 缓存监控
- **监控指标**
  - 缓存命中率：监控缓存的有效性
  - 缓存大小：防止内存泄漏
  - 缓存响应时间：确保缓存本身不成为瓶颈
  - 缓存过期率：评估缓存策略是否合理

- **监控工具**
  - 内置监控：实现简单的统计和日志记录
  - 专业工具：使用Prometheus + Grafana监控缓存性能
  - Redis监控：使用redis-cli、RedisInsight等工具

### 4.4 风险评估

| 风险 | 影响 | 可能性 | 缓解措施 |
|------|------|--------|----------|
| 数据库索引影响写入性能 | 中 | 中 | 选择合适的索引，避免过度索引 |
| 队列系统引入增加复杂性 | 高 | 中 | 充分测试，提供回退机制 |
| 并发控制调整影响系统稳定性 | 高 | 低 | 逐步调整，监控系统表现 |
| 代码重构引入新bug | 中 | 中 | 完善单元测试，增加代码审查 |
| 安全措施影响系统性能 | 中 | 低 | 平衡安全和性能，选择高效实现 |
| 缓存数据不一致 | 高 | 中 | 实现缓存失效策略，设置合理的过期时间 |
| 缓存导致内存占用过高 | 高 | 低 | 设置缓存大小限制，实现LRU淘汰策略 |
| 缓存穿透问题 | 中 | 中 | 实现空值缓存和布隆过滤器 |
| Redis服务故障影响系统 | 高 | 低 | 实现降级策略，提供本地缓存备份 |

## 五、测试与验证

### 5.1 性能测试

1. **基准测试**
   - 在优化前进行基准测试，记录关键指标
   - 测试场景：常规查询、大数据量查询、网络扫描、并发任务执行
   - 记录无缓存情况下的响应时间和资源占用

2. **对比测试**
   - 每项优化完成后进行对比测试
   - 记录性能提升百分比
   - 对比缓存前后的响应时间差异

3. **负载测试**
   - 模拟高负载场景，测试系统稳定性
   - 测试并发用户数：50/100/200
   - 测试缓存在高并发下的表现

4. **缓存性能测试**
   - 测试缓存命中率
   - 测试缓存响应时间
   - 测试不同缓存策略的效果
   - 测试缓存失效和更新机制

### 5.2 安全测试

1. **漏洞扫描**
   - 使用自动化工具进行漏洞扫描
   - 重点关注SQL注入、XSS等常见漏洞

2. **渗透测试**
   - 进行模拟攻击测试
   - 验证安全措施有效性

3. **代码审查**
   - 进行安全相关的代码审查
   - 重点关注敏感操作和数据处理

## 六、维护与监控

### 6.1 性能监控

1. **系统监控**
   - 监控CPU、内存、磁盘IO使用情况
   - 设置阈值报警
   - 监控Node.js进程内存使用

2. **数据库监控**
   - 监控慢查询
   - 监控连接池状态
   - 监控查询缓存效果

3. **API性能监控**
   - 记录API响应时间
   - 识别性能热点
   - 监控高频API的缓存效果

4. **缓存监控**
   - 监控缓存命中率和未命中率
   - 监控缓存内存占用
   - 监控缓存响应时间
   - 监控缓存过期和淘汰情况
   - 对Redis缓存进行专门监控

### 6.2 日志分析

1. **错误日志分析**
   - 定期分析错误日志
   - 识别常见问题模式

2. **性能日志分析**
   - 分析性能相关日志
   - 发现潜在优化点

### 6.3 定期评估

1. **季度性能评估**
   - 每季度进行一次全面性能评估
   - 根据评估结果调整优化计划

2. **安全评估**
   - 定期进行安全评估
   - 及时应用安全补丁

## 七、结论

本优化方案通过多方面的技术手段，旨在全面提升CMDB系统的性能和安全性。通过分阶段实施，可以在保证系统稳定运行的同时，逐步实现性能提升和安全加固。

优化完成后，预期达到以下目标：
1. 数据库查询性能提升50%以上
2. 网络扫描效率提升30%以上
3. 系统并发处理能力提升100%
4. 内存使用效率提升40%
5. 系统安全性显著增强
6. 高频API响应时间减少70%以上（通过缓存）
7. 用户权限验证速度提升80%（通过缓存）
8. 系统配置读取性能提升90%（通过缓存）
9. 仪表盘加载速度提升60%（通过缓存）

后续将根据系统运行情况和用户反馈，持续优化和改进系统性能和安全性。
