<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>变更详情</span>
          <div class="header-buttons">
            <el-button type="primary" @click="goBack">返回列表</el-button>
            <el-button type="success" @click="saveChange" :loading="saveLoading">保存</el-button>
          </div>
        </div>
      </template>

      <!-- 变更编号显示 -->
      <div class="change-id-section">
        <span class="change-id-text">变更编号：{{ changeData.change_id || '系统自动生成' }}</span>
      </div>

      <!-- 变更信息卡片 -->
      <div class="change-info-card">
        <div class="card-title">变更信息</div>
        <div class="card-content">
          <el-form
            ref="formRef"
            :model="changeData"
            :rules="rules"
            label-width="100px"
            label-position="right"
          >
            <!-- 第一行：变更名称 -->
            <el-row :gutter="16">
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="变更名称" prop="title">
                  <el-input v-model="changeData.title" placeholder="请输入变更名称" size="small" />
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="变更级别" prop="change_level">
                  <el-select
                    v-model="changeData.change_level"
                    filterable
                    placeholder="请选择变更级别"
                    style="width: 100%"
                    size="small">
                    <el-option
                      v-for="item in changeLevelOptions"
                      :key="item.dict_code"
                      :label="item.dict_name"
                      :value="item.dict_code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
            <!-- 第二行：计划时间、变更负责人 -->
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="计划时间" prop="planned_change_time">
                  <el-date-picker
                    v-model="changeData.planned_change_time"
                    type="date"
                    placeholder="选择日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                    size="small"
                  />
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="变更负责人" prop="requester">
                  <el-select
                    v-model="changeData.requester"
                    filterable
                    placeholder="请选择负责人"
                    style="width: 100%"
                    size="small">
                    <el-option
                      v-for="item in userOptions"
                      :key="item.username"
                      :label="item.real_name"
                      :value="item.username"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第三行：系统和人员 -->
            <el-row :gutter="16">
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="变更系统" prop="system">
                  <el-select
                    v-model="systemArray"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    filterable
                    placeholder="请选择变更系统"
                    style="width: 100%"
                    size="small"
                    @change="handleSystemChange"
                  >
                    <el-option
                      v-for="item in systemOptions"
                      :key="item.system_abbreviation"
                      :label="item.system_abbreviation"
                      :value="item.system_abbreviation"
                    />
                  </el-select>
                  <!-- 系统标签展示 -->
                  <div v-if="systemArray.length > 0" class="tag-display">
                    <el-tag
                      v-for="system in systemArray"
                      :key="system"
                      class="display-tag"
                      type="primary"
                      effect="light"
                      size="small"
                    >
                      {{ system }}
                    </el-tag>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="变更实施人" prop="implementers">
                  <el-select
                    v-model="implementersArray"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    filterable
                    placeholder="请选择实施人"
                    style="width: 100%"
                    size="small"
                    @change="handleImplementersChange"
                  >
                    <el-option
                      v-for="item in userOptions"
                      :key="item.username"
                      :label="item.real_name"
                      :value="item.username"
                    />
                  </el-select>
                  <!-- 实施人标签展示 -->
                  <div v-if="implementersArray.length > 0" class="tag-display">
                    <el-tag
                      v-for="username in implementersArray"
                      :key="username"
                      class="display-tag"
                      type="primary"
                      effect="light"
                      size="small"
                    >
                      {{ getUserRealName(username) }}
                    </el-tag>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>



      <!-- 附件管理组件 -->
      <file-attachments
        v-model:changeData="changeData"
        :refreshChangeData="refreshChangeData"
      />

      <!-- 删除按钮区域 -->
      <div v-if="changeData.id" class="delete-section">
        <el-divider />
        <div class="delete-button-container">
          <el-button
            type="danger"
            :disabled="!hasDeletePermission"
            :loading="deleteLoading"
            @click="handleDelete"
            class="delete-button"
          >
            <el-icon><Delete /></el-icon>
            删除变更记录
          </el-button>
          <div v-if="!hasDeletePermission" class="permission-tip">
            <el-text type="info" size="small">
              <el-icon><InfoFilled /></el-icon>
              您没有删除权限
            </el-text>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { Delete, InfoFilled } from '@element-plus/icons-vue'
import request from '@/utils/request'
import FileAttachments from './components/FileAttachments.vue'
import { safeFormatDateOnly } from '@/utils/dateUtils'

export default {
  name: 'ChangeManagementDetail',
  components: {
    FileAttachments,
    Delete,
    InfoFilled
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const formRef = ref(null)

    // 加载状态
    const saveLoading = ref(false)
    const deleteLoading = ref(false)

    // 权限控制
    const hasDeletePermission = ref(false)

    // 检查删除权限
    const checkDeletePermission = () => {
      const roleCode = localStorage.getItem('role_code')
      const username = localStorage.getItem('loginUsername')

      // admin用户或role_code包含'D'的用户有删除权限
      hasDeletePermission.value = username === 'admin' || (roleCode && roleCode.includes('D'))

      console.log('删除权限检查:', {
        username,
        roleCode,
        hasDeletePermission: hasDeletePermission.value
      })
    }

    // 变更数据
    const changeData = reactive({
      id: null,
      change_id: '',
      title: '',
      system: '',
      change_level: '',
      planned_change_time: '',
      requester: '',
      implementers: '',
      oa_process: false,
      oa_process_file: null,
      signed_archive: false,
      signed_archive_file: null,
      operation_sheet: null,
      supplementary_material: null
    })

    // 实施人数组（用于多选）
    const implementersArray = ref([])

    // 系统数组（用于多选）
    const systemArray = ref([])

    // 处理实施人变化
    const handleImplementersChange = (values) => {
      changeData.implementers = values.join(',')
    }

    // 处理系统变化
    const handleSystemChange = (values) => {
      changeData.system = values.join(',')
    }

    // 获取用户真实姓名
    const getUserRealName = (username) => {
      if (!username) return '未知用户'
      const user = userOptions.value.find(user => user.username === username)
      return user ? user.real_name : username
    }

    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入变更名称', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      system: [
        { required: true, message: '请选择变更系统', trigger: 'change' }
      ],
      change_level: [
        { required: true, message: '请选择变更级别', trigger: 'change' }
      ],
      planned_change_time: [
        { required: true, message: '请选择计划变更时间', trigger: 'change' }
      ],
      requester: [
        { required: true, message: '请选择变更负责人', trigger: 'change' }
      ],
      implementers: [
        { required: true, message: '请选择变更实施人', trigger: 'change' }
      ]
    }

    // 选项数据
    const systemOptions = ref([])
    const changeLevelOptions = ref([])
    const userOptions = ref([])

    // 获取系统列表
    const getSystemList = async () => {
      try {
        const response = await request({
          url: '/api/get_system_list',
          method: 'post'
        })

        if (response.code === 0) {
          systemOptions.value = response.msg
        }
      } catch (error) {
        console.error('获取系统列表失败:', error)
        ElMessage.error('获取系统列表失败')
      }
    }

    // 获取变更级别列表
    const getChangeLevelList = async () => {
      try {
        const response = await request({
          url: '/api/get_cmdb_data_dictionary',
          method: 'post',
          data: {
            dict_type: 'P'  // 使用P类型数据字典作为变更级别
          }
        })

        if (response.code === 0) {
          changeLevelOptions.value = response.msg
        }
      } catch (error) {
        console.error('获取变更级别列表失败:', error)
        ElMessage.error('获取变更级别列表失败')
      }
    }

    // 获取用户列表
    const getUserList = async () => {
      try {
        const response = await request({
          url: '/api/get_user_list',
          method: 'post'
        })

        if (response.code === 0) {
          userOptions.value = response.msg
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败')
      }
    }

    // 获取变更详情
    const getChangeDetail = async (id, showLoading = true, showMessage = true) => {
      let loading = null;
      if (showLoading) {
        loading = ElLoading.service({
          lock: true,
          text: '加载中...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      }

      try {
        console.log('获取变更详情，ID:', id);
        // 添加时间戳，避免缓存
        const timestamp = new Date().getTime();

        const response = await request({
          url: '/api/get_ops_change_management',
          method: 'post',
          data: {
            id: id,
            _t: timestamp // 添加时间戳参数，避免缓存
          },
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        console.log('获取变更详情响应:', response);

        if (response.code === 0 && response.msg.length > 0) {
          const data = response.msg[0];
          console.log('获取到的变更数据:', data);

          // 创建一个全新的对象，确保Vue能够检测到变化
          const newChangeData = {};

          // 更新表单数据
          Object.keys(changeData).forEach(key => {
            if (data[key] !== undefined) {
              // 特殊处理计划变更时间字段
              if (key === 'planned_change_time' && data[key]) {
                // 使用时区安全的日期转换函数
                const timeValue = data[key]
                console.log('处理计划时间字段:', timeValue, '类型:', typeof timeValue)

                newChangeData[key] = safeFormatDateOnly(timeValue)
                console.log('时区安全转换结果:', newChangeData[key])
              } else {
                newChangeData[key] = data[key]
              }
            } else {
              newChangeData[key] = changeData[key]
            }
          });

          console.log('更新前的数据:', JSON.stringify({
            oa_process: changeData.oa_process,
            oa_process_file: changeData.oa_process_file,
            signed_archive: changeData.signed_archive,
            signed_archive_file: changeData.signed_archive_file,
            operation_sheet: changeData.operation_sheet
          }));

          console.log('更新后的数据:', JSON.stringify({
            oa_process: newChangeData.oa_process,
            oa_process_file: newChangeData.oa_process_file,
            signed_archive: newChangeData.signed_archive,
            signed_archive_file: newChangeData.signed_archive_file,
            operation_sheet: newChangeData.operation_sheet
          }));

          // 使用Object.assign更新原始对象，触发响应式更新
          Object.assign(changeData, newChangeData);

          // 更新实施人数组
          if (changeData.implementers) {
            implementersArray.value = changeData.implementers.split(',');
          }

          // 更新系统数组
          if (changeData.system) {
            systemArray.value = changeData.system.split(',');
          }

          if (showMessage) {
            ElMessage.success('数据已刷新');
          }

          return true;
        } else {
          if (showMessage) {
            ElMessage.error('未找到变更记录');
          }
          router.push('/ops_change_management');
          return false;
        }
      } catch (error) {
        console.error('获取变更详情失败:', error);
        if (showMessage) {
          ElMessage.error('获取变更详情失败');
        }
        router.push('/ops_change_management');
        return false;
      } finally {
        if (loading) {
          loading.close();
        }
      }
    }

    // 刷新变更详情数据
    const refreshChangeData = async () => {
      console.log('刷新变更详情数据');
      if (!changeData.id) {
        console.log('变更ID为空，无法刷新数据');
        return false;
      }

      return await getChangeDetail(changeData.id, false, true);
    }

    // 保存变更
    const saveChange = async () => {
      if (!formRef.value) return

      await formRef.value.validate(async (valid) => {
        if (!valid) {
          ElMessage.error('请检查表单填写是否正确')
          return
        }

        saveLoading.value = true

        try {
          const url = changeData.id
            ? '/api/update_ops_change_management'
            : '/api/add_ops_change_management'

          // 获取当前登录用户名
          const currentUsername = localStorage.getItem('loginUsername') || 'admin';

          // 转换字段名称以匹配后端期望的格式
          const data = {
            ...changeData,
            changeLevel: changeData.change_level,
            plannedChangeTime: changeData.planned_change_time,
            usernameby: currentUsername,
            created_by: currentUsername,  // 确保created_by字段设置为当前用户
            updated_by: currentUsername   // 确保updated_by字段设置为当前用户
          }

          // 打印请求数据，用于调试
          console.log('发送请求数据:', data)

          const response = await request({
            url: url,
            method: 'post',
            data: data
          })

          if (response.code === 0) {
            ElMessage.success('保存成功')

            if (!changeData.id) {
              // 如果是新增，更新ID和变更编号
              changeData.id = response.msg.id
              changeData.change_id = response.msg.change_id
            }
          } else {
            ElMessage.error(`保存失败: ${response.msg}`)
          }
        } catch (error) {
          console.error('保存变更失败:', error)
          ElMessage.error('保存变更失败')
        } finally {
          saveLoading.value = false
        }
      })
    }

    // 返回列表
    const goBack = () => {
      router.push('/ops_change_management')
    }

    // 删除变更记录
    const handleDelete = () => {
      if (!hasDeletePermission.value) {
        ElMessage.error('您没有删除权限')
        return
      }

      if (!changeData.id) {
        ElMessage.error('无法删除：变更记录ID不存在')
        return
      }

      ElMessageBox.confirm(
        `确定要删除变更记录 "${changeData.change_id}" 吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      ).then(async () => {
        deleteLoading.value = true

        try {
          const response = await request({
            url: '/api/del_ops_change_management',
            method: 'post',
            data: {
              id: changeData.id,
              usernameby: localStorage.getItem('loginUsername') || 'admin'
            }
          })

          if (response.code === 0) {
            ElMessage.success('删除成功')
            // 删除成功后返回列表页面
            router.push('/ops_change_management')
          } else {
            ElMessage.error(`删除失败: ${response.msg}`)
          }
        } catch (error) {
          console.error('删除变更记录失败:', error)
          ElMessage.error('删除变更记录失败')
        } finally {
          deleteLoading.value = false
        }
      }).catch(() => {
        // 用户取消删除操作
        console.log('用户取消删除操作')
      })
    }

    // 页面加载时执行
    onMounted(async () => {
      // 检查删除权限
      checkDeletePermission()

      // 获取选项数据
      await Promise.all([
        getSystemList(),
        getChangeLevelList(),
        getUserList()
      ])

      // 获取路由参数
      const id = route.params.id

      if (id && id !== 'new') {
        // 编辑模式
        await getChangeDetail(id)
      } else {
        // 新增模式
        // 默认选择当前用户作为变更负责人
        const username = localStorage.getItem('username')
        if (username) {
          changeData.requester = username
        }
      }
    })

    return {
      formRef,
      changeData,
      implementersArray,
      systemArray,
      rules,
      systemOptions,
      changeLevelOptions,
      userOptions,
      saveLoading,
      deleteLoading,
      hasDeletePermission,
      handleImplementersChange,
      handleSystemChange,
      getUserRealName,
      saveChange,
      goBack,
      handleDelete,
      refreshChangeData
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

/* 变更编号显示 */
.change-id-section {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.change-id-text {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* 变更信息卡片 */
.change-info-card {
  margin-top: 20px;
  margin-bottom: 20px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.change-info-card .card-title {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  border-radius: 6px 6px 0 0;
}

.change-info-card .card-content {
  padding: 16px;
}



.tag-display {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  min-height: 20px;
}

.display-tag {
  font-size: 12px;
  border-radius: 4px;
  padding: 2px 6px;
}

/* 删除按钮区域样式 */
.delete-section {
  margin-top: 20px;
}

.delete-button-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  padding: 16px 0;
}

.delete-button {
  min-width: 140px;
}

.permission-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-buttons {
    width: 100%;
    justify-content: flex-end;
  }

  .change-info-card .card-content {
    padding: 12px;
  }

  .delete-button-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .change-id-section {
    padding: 10px 12px;
  }

  .change-id-text {
    font-size: 13px;
  }
}
</style>
