/**
 * 变更管理控制器
 */
const { connPG } = require('../db/pg');
const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');
const { upload, getFileUrl, deleteFile } = require('../utils/file-upload');

// 初始化数据库表和序列
const initDatabase = async () => {
  try {
    // 检查表是否存在
    const tableCheckResult = await connPG.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'ops_change_management'
      );
    `);

    const tableExists = tableCheckResult.rows[0].exists;

    if (!tableExists) {
      console.log('创建变更管理表...');

      // 创建表
      await connPG.query(`
        CREATE TABLE "public"."ops_change_management" (
          "id" SERIAL PRIMARY KEY,
          "change_id" varchar(20) NOT NULL,
          "title" varchar(255) NOT NULL,
          "system" varchar(100) NOT NULL,
          "change_level" varchar(10) NOT NULL,
          "planned_change_time" date NOT NULL,
          "requester" varchar(50) NOT NULL,
          "implementers" text NOT NULL,
          "description" text,
          "oa_process" boolean DEFAULT false,
          "oa_process_file" varchar(255),
          "signed_archive" boolean DEFAULT false,
          "signed_archive_file" varchar(255),
          "operation_sheet" varchar(255),
          "supplementary_material" varchar(255),
          "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
          "created_by" varchar(50),
          "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
          "updated_by" varchar(50),
          "del_flag" char(1) DEFAULT '0'
        );
      `);

      // 添加注释
      await connPG.query(`
        COMMENT ON COLUMN "public"."ops_change_management"."change_id" IS '变更编号，格式：BG-YYYYMMDD-序号';
        COMMENT ON COLUMN "public"."ops_change_management"."title" IS '变更名称';
        COMMENT ON COLUMN "public"."ops_change_management"."system" IS '所属系统，来自系统管理员责任表（公司）的业务系统简称';
        COMMENT ON COLUMN "public"."ops_change_management"."change_level" IS '变更级别，使用数据字典P';
        COMMENT ON COLUMN "public"."ops_change_management"."planned_change_time" IS '计划变更时间';
        COMMENT ON COLUMN "public"."ops_change_management"."requester" IS '变更负责人，存储username';
        COMMENT ON COLUMN "public"."ops_change_management"."implementers" IS '变更实施人，存储多个username，以逗号分隔';
        COMMENT ON COLUMN "public"."ops_change_management"."description" IS '变更描述';
        COMMENT ON COLUMN "public"."ops_change_management"."oa_process" IS 'OA流程是否上传';
        COMMENT ON COLUMN "public"."ops_change_management"."oa_process_file" IS 'OA流程文件路径';
        COMMENT ON COLUMN "public"."ops_change_management"."signed_archive" IS '签字存档是否上传';
        COMMENT ON COLUMN "public"."ops_change_management"."signed_archive_file" IS '签字存档文件路径';
        COMMENT ON COLUMN "public"."ops_change_management"."operation_sheet" IS '变更操作表文件路径';
      `);

      // 创建索引
      await connPG.query(`
        CREATE INDEX "idx_ops_change_management_change_id" ON "public"."ops_change_management" ("change_id");
        CREATE INDEX "idx_ops_change_management_system" ON "public"."ops_change_management" ("system");
        CREATE INDEX "idx_ops_change_management_change_level" ON "public"."ops_change_management" ("change_level");
        CREATE INDEX "idx_ops_change_management_requester" ON "public"."ops_change_management" ("requester");
        CREATE INDEX "idx_ops_change_management_implementers" ON "public"."ops_change_management" USING gin(to_tsvector('simple', "implementers"));
        CREATE INDEX "idx_ops_change_management_del_flag" ON "public"."ops_change_management" ("del_flag");
      `);
    }

    // 检查序列是否存在
    const seqCheckResult = await connPG.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.sequences
        WHERE sequence_schema = 'public'
        AND sequence_name = 'ops_change_id_seq'
      );
    `);

    const seqExists = seqCheckResult.rows[0].exists;

    if (!seqExists) {
      console.log('创建变更编号序列...');

      // 创建序列
      await connPG.query(`
        CREATE SEQUENCE "public"."ops_change_id_seq"
          INCREMENT 1
          MINVALUE 1
          MAXVALUE 9999
          START 1
          CACHE 1
          CYCLE;
      `);
    }

    // 检查视图是否存在
    const viewCheckResult = await connPG.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.views
        WHERE table_schema = 'public'
        AND table_name = 'v_ops_change_management'
      );
    `);

    const viewExists = viewCheckResult.rows[0].exists;

    if (!viewExists) {
      console.log('创建变更管理视图...');

      // 创建视图
      await connPG.query(`
        CREATE VIEW "public"."v_ops_change_management" AS
        SELECT
          t.id,
          t.change_id,
          t.title,
          t.system,
          t.change_level,
          COALESCE(t2.dict_name, t.change_level) AS change_level_name_display,
          t.planned_change_time,
          to_char(t.planned_change_time, 'YYYY-MM-DD') || ' ' ||
          CASE EXTRACT(DOW FROM t.planned_change_time)
            WHEN 0 THEN '星期日'
            WHEN 1 THEN '星期一'
            WHEN 2 THEN '星期二'
            WHEN 3 THEN '星期三'
            WHEN 4 THEN '星期四'
            WHEN 5 THEN '星期五'
            WHEN 6 THEN '星期六'
          END AS formatted_change_time,
          t.requester,
          u1.real_name AS requester_name,
          t.implementers,
          (
            SELECT string_agg(u.real_name, ', ')
            FROM (
              SELECT unnest(string_to_array(t.implementers, ',')) AS username
            ) AS usernames
            LEFT JOIN cmdb_users u ON u.username = usernames.username AND u.del_flag = '0'
          ) AS implementers_name,
          t.description,
          t.oa_process,
          t.oa_process_file,
          t.signed_archive,
          t.signed_archive_file,
          t.operation_sheet,
          t.supplementary_material,
          t.created_at,
          t.created_by,
          t.updated_at,
          t.updated_by
        FROM
          ops_change_management t
        LEFT JOIN
          cmdb_data_dictionary t2 ON t2.dict_type = 'P' AND t2.dict_code = t.change_level AND t2.del_flag = '0'
        LEFT JOIN
          cmdb_users u1 ON u1.username = t.requester AND u1.del_flag = '0'
        WHERE
          t.del_flag = '0';
      `);
    }

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('初始化数据库失败:', error);
  }
};

// 执行初始化
initDatabase();

// 处理上传的文件信息
const processUploadedFile = async (file, formData) => {
  try {
    console.log('处理上传的文件信息:', file);
    console.log('表单数据:', formData);

    if (!file) {
      throw new Error('未找到上传的文件');
    }

    // 处理文件名乱码问题
    let originalname = file.originalname;
    try {
      // 尝试解码文件名
      if (Buffer.from(originalname).toString('utf8') !== originalname) {
        originalname = Buffer.from(originalname, 'latin1').toString('utf8');
      }
    } catch (error) {
      console.error('文件名解码失败:', error);
    }

    console.log('原始文件名:', file.originalname);
    console.log('处理后文件名:', originalname);

    // 确保上传目录存在
    const { fileType } = formData;
    let uploadPath;

    // 创建按年月组织的子目录
    const yearMonth = format(new Date(), 'yyyy-MM');

    switch (fileType) {
      case 'oa_process':
        uploadPath = path.join(process.env.FILE_UPLOAD_OA_PATH, yearMonth);
        break;
      case 'signed_archive':
        uploadPath = path.join(process.env.FILE_UPLOAD_SIGNED_PATH, yearMonth);
        break;
      case 'operation_sheet':
        uploadPath = path.join(process.env.FILE_UPLOAD_OPERATION_PATH, yearMonth);
        break;
      case 'supplementary_material':
        uploadPath = path.join(process.env.FILE_UPLOAD_SUPPLEMENTARY_PATH, yearMonth);
        break;
      default:
        uploadPath = path.join(process.env.FILE_UPLOAD_BASE_PATH, yearMonth);
    }

    // 确保目录存在
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    // 获取文件相对路径（相对于上传根目录）
    const relativePath = path.relative(
      path.resolve(process.env.FILE_UPLOAD_BASE_PATH),
      path.resolve(file.path)
    ).replace(/\\/g, '/');

    // 构建文件URL
    const fileUrl = getFileUrl(relativePath);

    return {
      originalname: originalname, // 使用处理后的文件名
      filename: file.filename,
      path: relativePath,
      url: fileUrl
    };
  } catch (error) {
    console.error('处理上传的文件信息失败:', error);
    throw error;
  }
};

// 生成变更编号
const generateChangeId = async () => {
  try {
    // 获取当前日期
    const today = new Date();
    const dateStr = format(today, 'yyyyMMdd');

    let seqValue;

    try {
      // 尝试获取序列的下一个值
      const seqResult = await connPG.query('SELECT nextval(\'ops_change_id_seq\')');
      seqValue = seqResult.rows[0].nextval;
    } catch (seqError) {
      console.error('序列ops_change_id_seq不存在，尝试创建序列:', seqError);

      // 尝试创建序列
      try {
        await connPG.query(`
          CREATE SEQUENCE IF NOT EXISTS "public"."ops_change_id_seq"
            INCREMENT 1
            MINVALUE 1
            MAXVALUE 9999
            START 1
            CACHE 1
            CYCLE;
        `);

        // 再次尝试获取序列值
        const seqResult = await connPG.query('SELECT nextval(\'ops_change_id_seq\')');
        seqValue = seqResult.rows[0].nextval;
      } catch (createError) {
        console.error('创建序列失败，使用随机数作为替代:', createError);

        // 如果创建序列也失败，使用随机数作为替代
        // 生成1到9999之间的随机数
        seqValue = Math.floor(Math.random() * 9999) + 1;
      }
    }

    // 格式化序列号为4位数，不足前面补0
    const seqStr = String(seqValue).padStart(4, '0');

    // 组合成变更编号: BG-YYYYMMDD-序号
    return `BG-${dateStr}-${seqStr}`;
  } catch (error) {
    console.error('生成变更编号失败:', error);
    throw error;
  }
};

// 获取变更管理列表
const getChangeManagementList = async (req, res) => {
  try {
    // 打印请求体，用于调试
    console.log('请求体:', req.body);

    const {
      id, // 添加ID参数，用于获取单个变更记录
      changeLevel,
      change_level, // 添加备用参数名
      system, // 添加变更系统参数
      requester,
      implementer,
      oaProcess,
      signedArchive,
      startDate,
      endDate,
      keyword,
      currentPage = 1,
      pageSize = 10,
      sortProp = 'change_id',
      sortOrder = 'desc'
    } = req.body;

    // 构建查询条件
    const conditions = [];
    const params = [];
    let paramIndex = 1;

    // 如果提供了ID参数，优先按ID查询单个记录
    if (id) {
      console.log('使用ID过滤:', id);
      conditions.push(`id = $${paramIndex++}`);
      params.push(id);
    }

    // 变更级别 - 支持两种参数名
    const effectiveChangeLevel = changeLevel || change_level;
    if (effectiveChangeLevel) {
      console.log('使用变更级别过滤:', effectiveChangeLevel);
      conditions.push(`change_level = $${paramIndex++}`);
      params.push(effectiveChangeLevel);
    }

    // 变更系统
    if (system) {
      console.log('使用变更系统过滤:', system);
      conditions.push(`system LIKE $${paramIndex++}`);
      params.push(`%${system}%`);
    }

    // 变更负责人
    if (requester) {
      conditions.push(`(requester = $${paramIndex++} OR requester_name LIKE $${paramIndex++})`);
      params.push(requester);
      params.push(`%${requester}%`);
    }

    // 变更实施人
    if (implementer) {
      conditions.push(`(implementers LIKE $${paramIndex++} OR implementers_name LIKE $${paramIndex++})`);
      params.push(`%${implementer}%`);
      params.push(`%${implementer}%`);
    }

    // OA流程
    if (oaProcess === '已上传' || oaProcess === '未上传') {
      conditions.push(`oa_process = $${paramIndex++}`);
      params.push(oaProcess === '已上传');
    }

    // 签字存档
    if (signedArchive === '已上传' || signedArchive === '未上传') {
      conditions.push(`signed_archive = $${paramIndex++}`);
      params.push(signedArchive === '已上传');
    }

    // 日期范围
    if (startDate && endDate) {
      conditions.push(`planned_change_time BETWEEN $${paramIndex++} AND $${paramIndex++}`);
      params.push(startDate);
      params.push(endDate);
    } else if (startDate) {
      conditions.push(`planned_change_time >= $${paramIndex++}`);
      params.push(startDate);
    } else if (endDate) {
      conditions.push(`planned_change_time <= $${paramIndex++}`);
      params.push(endDate);
    }

    // 关键词搜索（变更编号或变更名称）
    if (keyword) {
      conditions.push(`(change_id ILIKE $${paramIndex++} OR title ILIKE $${paramIndex++})`);
      params.push(`%${keyword}%`);
      params.push(`%${keyword}%`);
    }

    // 构建WHERE子句
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // 构建排序子句
    const orderClause = `ORDER BY ${sortProp} ${sortOrder.toUpperCase()}`;

    // 查询总记录数 - 使用视图
    const countQuery = `
      SELECT COUNT(*) AS total
      FROM v_ops_change_management
      ${whereClause}
    `;

    const countResult = await connPG.query(countQuery, params);
    const total = parseInt(countResult.rows[0].total);

    // 计算分页参数
    const offset = (currentPage - 1) * pageSize;

    // 查询分页数据 - 使用视图
    const dataQuery = `
      SELECT
        id,
        change_id,
        title,
        system,
        change_level,
        change_level_name_display,
        planned_change_time,
        formatted_change_time,
        requester,
        requester_name,
        implementers,
        implementers_name,
        oa_process,
        oa_process_file,
        signed_archive,
        signed_archive_file,
        operation_sheet,
        supplementary_material,
        created_at,
        created_by,
        updated_at,
        updated_by
      FROM v_ops_change_management
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    const dataParams = [...params, pageSize, offset];
    const dataResult = await connPG.query(dataQuery, dataParams);

    // 返回结果
    res.json({
      code: 0,
      msg: dataResult.rows,
      total
    });
  } catch (error) {
    console.error('获取变更管理列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取系统列表（从系统管理员责任表公司中获取）
const getSystemList = async (_, res) => {
  try {
    const query = `
      SELECT system_abbreviation
      FROM cmdb_system_admin_responsibility_company
      WHERE del_flag = '0'
      ORDER BY system_abbreviation
    `;

    const result = await connPG.query(query);

    res.json({
      code: 0,
      msg: result.rows
    });
  } catch (error) {
    console.error('获取系统列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 获取用户列表
const getUserList = async (_, res) => {
  try {
    const query = `
      SELECT username, real_name
      FROM cmdb_users
      WHERE del_flag = '0'
      ORDER BY username
    `;

    const result = await connPG.query(query);

    res.json({
      code: 0,
      msg: result.rows
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 添加变更管理记录
const addChangeManagement = async (req, res) => {
  try {
    // 打印请求体，用于调试
    console.log('添加变更管理记录请求体:', req.body);

    const {
      title,
      system,
      changeLevel,
      change_level, // 支持前端使用下划线命名
      plannedChangeTime,
      planned_change_time, // 支持前端使用下划线命名
      requester,
      implementers,
      usernameby,
      created_by,
      updated_by
    } = req.body;

    // 使用驼峰命名或下划线命名，以支持不同的前端命名风格
    const effectiveChangeLevel = changeLevel || change_level;
    const effectivePlannedChangeTime = plannedChangeTime || planned_change_time;

    // 生成变更编号
    const changeId = await generateChangeId();

    // 构建插入语句
    const query = `
      INSERT INTO ops_change_management (
        change_id,
        title,
        system,
        change_level,
        planned_change_time,
        requester,
        implementers,
        created_by,
        updated_by
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    // 确定创建者和更新者
    const effectiveCreatedBy = created_by || usernameby || 'admin';
    const effectiveUpdatedBy = updated_by || usernameby || 'admin';

    const params = [
      changeId,
      title,
      system,
      effectiveChangeLevel, // 使用有效的变更级别
      effectivePlannedChangeTime, // 使用有效的计划变更时间
      requester,
      implementers, // 使用多选的实施人，以逗号分隔的字符串
      effectiveCreatedBy,
      effectiveUpdatedBy
    ];

    const result = await connPG.query(query, params);

    // 记录用户操作日志
    try {
      const logQuery = `
        INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      const logBody = {
        action: '新增变更记录',
        change_id: changeId,
        title: title,
        system: system,
        change_level: effectiveChangeLevel
      };

      await connPG.query(logQuery, [
        'POST',
        '/api/add_ops_change_management',
        JSON.stringify(logBody),
        effectiveCreatedBy,
        'add'
      ]);

      console.log(`已记录用户操作日志: ${effectiveCreatedBy} 新增了变更记录 ${changeId}`);
    } catch (logError) {
      console.error('记录用户操作日志失败:', logError);
      // 日志记录失败不影响主要功能
    }

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    console.error('添加变更管理记录失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 上传文件
const uploadFile = async (req, res) => {
  try {
    // 直接使用multer处理上传，而不是先检查req.body
    // multer会将文件信息存储在req.file中，将表单字段存储在req.body中
    const fileUploadMiddleware = upload.single('file');

    await new Promise((resolve, reject) => {
      fileUploadMiddleware(req, res, (err) => {
        if (err) {
          console.error('文件上传中间件错误:', err);
          return reject(err);
        }
        resolve();
      });
    });

    console.log('文件上传中间件处理完成');
    console.log('收到文件上传请求，请求体:', req.body);
    console.log('文件信息:', req.file);
    console.log('请求头:', req.headers);

    const { changeId, fileType } = req.body;

    console.log('变更ID:', changeId);
    console.log('文件类型:', fileType);

    if (!changeId) {
      console.log('错误: 缺少变更编号参数');
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      console.log('错误: 无效的文件类型:', fileType);
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    if (!req.file) {
      console.log('错误: 未找到上传的文件');
      return res.status(400).json({ code: 1, msg: '未找到上传的文件' });
    }

    console.log('开始处理文件信息...');
    // 处理文件信息
    const fileInfo = await processUploadedFile(req.file, req.body);


    // 更新数据库记录
    let updateQuery;
    let updateParams;

    if (fileType === 'oa_process') {
      updateQuery = `
        UPDATE ops_change_management
        SET oa_process = true, oa_process_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.usernameby || 'admin', changeId];
    } else if (fileType === 'signed_archive') {
      updateQuery = `
        UPDATE ops_change_management
        SET signed_archive = true, signed_archive_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.usernameby || 'admin', changeId];
    } else if (fileType === 'operation_sheet') {
      updateQuery = `
        UPDATE ops_change_management
        SET operation_sheet = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.usernameby || 'admin', changeId];
    } else if (fileType === 'supplementary_material') {
      updateQuery = `
        UPDATE ops_change_management
        SET supplementary_material = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, req.body.usernameby || 'admin', changeId];
    }

    console.log('执行数据库更新，SQL:', updateQuery);
    console.log('参数:', updateParams);

    const result = await connPG.query(updateQuery, updateParams);
    console.log('数据库更新结果:', result.rows);

    if (result.rows.length === 0) {
      console.log('错误: 未找到指定的变更记录');
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录' });
    }

    console.log('文件上传成功，返回结果');
    res.json({
      code: 0,
      msg: {
        ...fileInfo,
        changeId,
        fileType
      }
    });
  } catch (error) {
    console.error('文件上传失败:', error);
    console.error('错误堆栈:', error.stack);

    // 返回更详细的错误信息
    res.status(500).json({
      code: 1,
      msg: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      details: '文件上传失败，请检查服务器日志'
    });
  }
};

// 简化版文件上传API
const uploadFileSimple = async (req, res) => {
  try {
    console.log('收到简化版文件上传请求');

    // 使用multer处理文件上传
    const fileUploadMiddleware = upload.single('file');

    await new Promise((resolve, reject) => {
      fileUploadMiddleware(req, res, (err) => {
        if (err) {
          console.error('文件上传中间件错误:', err);
          return reject(err);
        }
        resolve();
      });
    });

    console.log('文件上传中间件处理完成');
    console.log('请求查询参数:', req.query);
    console.log('请求体:', req.body);
    console.log('文件信息:', req.file);

    // 从URL查询参数获取变更ID和文件类型
    const changeId = req.query.changeId || req.body.changeId;
    const fileType = req.query.fileType || req.body.fileType;
    const usernameby = req.query.usernameby || req.body.usernameby || 'admin';

    console.log('变更ID:', changeId);
    console.log('文件类型:', fileType);
    console.log('用户名:', usernameby);

    if (!changeId) {
      console.log('错误: 缺少变更编号参数');
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      console.log('错误: 无效的文件类型:', fileType);
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    if (!req.file) {
      console.log('错误: 未找到上传的文件');
      return res.status(400).json({ code: 1, msg: '未找到上传的文件' });
    }

    // 处理文件信息
    const fileInfo = await processUploadedFile(req.file, { changeId, fileType, usernameby });

    // 更新数据库记录
    let updateQuery;
    let updateParams;

    if (fileType === 'oa_process') {
      updateQuery = `
        UPDATE ops_change_management
        SET oa_process = true, oa_process_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, usernameby, changeId];
    } else if (fileType === 'signed_archive') {
      updateQuery = `
        UPDATE ops_change_management
        SET signed_archive = true, signed_archive_file = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, usernameby, changeId];
    } else if (fileType === 'operation_sheet') {
      updateQuery = `
        UPDATE ops_change_management
        SET operation_sheet = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, usernameby, changeId];
    } else if (fileType === 'supplementary_material') {
      updateQuery = `
        UPDATE ops_change_management
        SET supplementary_material = $1, updated_at = CURRENT_TIMESTAMP, updated_by = $2
        WHERE change_id = $3
        RETURNING *
      `;
      updateParams = [fileInfo.path, usernameby, changeId];
    }

    const result = await connPG.query(updateQuery, updateParams);

    if (result.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录' });
    }

    res.json({
      code: 0,
      msg: {
        ...fileInfo,
        changeId,
        fileType
      }
    });
  } catch (error) {
    console.error('简化版文件上传失败:', error);
    console.error('错误堆栈:', error.stack);

    // 返回更详细的错误信息
    res.status(500).json({
      code: 1,
      msg: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      details: '文件上传失败，请检查服务器日志'
    });
  }
};

// 下载文件
const downloadFile = async (req, res) => {
  try {
    const { changeId, fileType, direct } = req.query;

    if (!changeId) {
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    // 查询文件路径
    let query;
    if (fileType === 'oa_process') {
      query = 'SELECT oa_process_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'signed_archive') {
      query = 'SELECT signed_archive_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'operation_sheet') {
      query = 'SELECT operation_sheet AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'supplementary_material') {
      query = 'SELECT supplementary_material AS file_path FROM ops_change_management WHERE change_id = $1';
    }

    const result = await connPG.query(query, [changeId]);

    if (result.rows.length === 0 || !result.rows[0].file_path) {
      return res.status(404).json({ code: 1, msg: '未找到指定的文件' });
    }

    const filePath = result.rows[0].file_path;
    const fullPath = path.join(process.env.FILE_UPLOAD_BASE_PATH, filePath);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ code: 1, msg: '文件不存在' });
    }

    // 检查是否是直接下载请求
    if (direct === 'true') {
      // 直接提供文件下载，而不是返回URL
      console.log('直接提供文件下载:', fullPath);

      // 从文件路径中提取原始文件名
      const fileName = path.basename(filePath);

      // 设置响应头，强制下载
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      // 创建文件读取流并通过管道传输到响应
      const fileStream = fs.createReadStream(fullPath);
      fileStream.pipe(res);

      // 处理错误
      fileStream.on('error', (err) => {
        console.error('文件流错误:', err);
        if (!res.headersSent) {
          res.status(500).json({ code: 1, msg: '文件读取失败' });
        }
      });

      return; // 直接返回，不执行后面的代码
    }

    // 获取文件URL
    const fileUrl = getFileUrl(filePath);

    // 返回文件URL和直接下载URL
    res.json({
      code: 0,
      msg: {
        url: fileUrl,
        path: filePath,
        directDownloadUrl: `/api/download_ops_change_file?changeId=${changeId}&fileType=${fileType}&direct=true`
      }
    });
  } catch (error) {
    console.error('获取文件下载链接失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 删除文件
const removeFile = async (req, res) => {
  try {
    const { changeId, fileType } = req.body;

    if (!changeId) {
      return res.status(400).json({ code: 1, msg: '缺少变更编号参数' });
    }

    if (!['oa_process', 'signed_archive', 'operation_sheet', 'supplementary_material'].includes(fileType)) {
      return res.status(400).json({ code: 1, msg: '无效的文件类型' });
    }

    // 查询文件路径
    let query;
    if (fileType === 'oa_process') {
      query = 'SELECT oa_process_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'signed_archive') {
      query = 'SELECT signed_archive_file AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'operation_sheet') {
      query = 'SELECT operation_sheet AS file_path FROM ops_change_management WHERE change_id = $1';
    } else if (fileType === 'supplementary_material') {
      query = 'SELECT supplementary_material AS file_path FROM ops_change_management WHERE change_id = $1';
    }

    const result = await connPG.query(query, [changeId]);

    if (result.rows.length === 0 || !result.rows[0].file_path) {
      return res.status(404).json({ code: 1, msg: '未找到指定的文件' });
    }

    const filePath = result.rows[0].file_path;

    // 删除文件
    const deleted = await deleteFile(filePath);

    if (!deleted) {
      return res.status(500).json({ code: 1, msg: '文件删除失败' });
    }

    // 更新数据库记录
    let updateQuery;
    if (fileType === 'oa_process') {
      updateQuery = `
        UPDATE ops_change_management
        SET oa_process = false, oa_process_file = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    } else if (fileType === 'signed_archive') {
      updateQuery = `
        UPDATE ops_change_management
        SET signed_archive = false, signed_archive_file = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    } else if (fileType === 'operation_sheet') {
      updateQuery = `
        UPDATE ops_change_management
        SET operation_sheet = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    } else if (fileType === 'supplementary_material') {
      updateQuery = `
        UPDATE ops_change_management
        SET supplementary_material = NULL, updated_at = CURRENT_TIMESTAMP, updated_by = $1
        WHERE change_id = $2
        RETURNING *
      `;
    }

    const updateResult = await connPG.query(updateQuery, [req.body.usernameby || 'admin', changeId]);

    if (updateResult.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录' });
    }

    res.json({
      code: 0,
      msg: '文件删除成功'
    });
  } catch (error) {
    console.error('文件删除失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

// 更新变更管理记录
const updateChangeManagement = async (req, res) => {
  try {
    // 打印请求体，用于调试
    console.log('更新变更管理记录请求体:', req.body);

    const {
      id,
      title,
      system,
      changeLevel,
      change_level, // 支持前端使用下划线命名
      plannedChangeTime,
      planned_change_time, // 支持前端使用下划线命名
      requester,
      implementers,
      usernameby,
      updated_by
    } = req.body;

    // 使用驼峰命名或下划线命名，以支持不同的前端命名风格
    const effectiveChangeLevel = changeLevel || change_level;
    const effectivePlannedChangeTime = plannedChangeTime || planned_change_time;

    // 构建更新语句
    const query = `
      UPDATE ops_change_management
      SET
        title = $1,
        system = $2,
        change_level = $3,
        planned_change_time = $4,
        requester = $5,
        implementers = $6,
        updated_at = CURRENT_TIMESTAMP,
        updated_by = $7
      WHERE id = $8
      RETURNING *
    `;

    // 确定更新者
    const effectiveUpdatedBy = updated_by || usernameby || 'admin';

    const params = [
      title,
      system,
      effectiveChangeLevel, // 使用有效的变更级别
      effectivePlannedChangeTime, // 使用有效的计划变更时间
      requester,
      implementers, // 使用多选的实施人，以逗号分隔的字符串
      effectiveUpdatedBy,
      id
    ];

    const result = await connPG.query(query, params);

    if (result.rowCount === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定ID的变更记录' });
    }

    // 记录用户操作日志
    try {
      const logQuery = `
        INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      const logBody = {
        action: '更新变更记录',
        change_id: result.rows[0].change_id,
        title: title,
        system: system,
        change_level: effectiveChangeLevel
      };

      await connPG.query(logQuery, [
        'POST',
        '/api/update_ops_change_management',
        JSON.stringify(logBody),
        effectiveUpdatedBy,
        'update'
      ]);

      console.log(`已记录用户操作日志: ${effectiveUpdatedBy} 更新了变更记录 ${result.rows[0].change_id}`);
    } catch (logError) {
      console.error('记录用户操作日志失败:', logError);
      // 日志记录失败不影响主要功能
    }

    res.json({
      code: 0,
      msg: result.rows[0]
    });
  } catch (error) {
    console.error('更新变更管理记录失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};



// 删除变更管理记录
const deleteChangeManagement = async (req, res) => {
  try {
    const { id, usernameby } = req.body;

    if (!id) {
      return res.status(400).json({ code: 1, msg: '缺少变更记录ID参数' });
    }

    // 获取当前登录用户的username
    const currentUsername = usernameby || req.headers['x-username'] || req.query.username || 'admin';

    // 先查询要删除的记录信息，用于日志记录
    const selectQuery = `
      SELECT change_id, title, system, change_level
      FROM ops_change_management
      WHERE id = $1 AND del_flag = '0'
    `;
    const selectResult = await connPG.query(selectQuery, [id]);

    if (selectResult.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '未找到指定的变更记录或记录已删除' });
    }

    const changeRecord = selectResult.rows[0];

    // 逻辑删除记录
    const deleteQuery = `
      UPDATE ops_change_management
      SET del_flag = '1', updated_at = CURRENT_TIMESTAMP, updated_by = $1
      WHERE id = $2
      RETURNING *
    `;
    const deleteResult = await connPG.query(deleteQuery, [currentUsername, id]);

    if (deleteResult.rows.length === 0) {
      return res.status(404).json({ code: 1, msg: '删除失败，未找到指定的变更记录' });
    }

    // 记录用户操作日志
    try {
      const logQuery = `
        INSERT INTO cmdb_user_logs (method, url, body, username, operation_type, timestamp)
        VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      const logBody = {
        action: '删除变更记录',
        change_id: changeRecord.change_id,
        title: changeRecord.title,
        system: changeRecord.system,
        change_level: changeRecord.change_level
      };

      await connPG.query(logQuery, [
        'POST',
        '/api/del_ops_change_management',
        JSON.stringify(logBody),
        currentUsername,
        'delete'
      ]);

      console.log(`已记录用户操作日志: ${currentUsername} 删除了变更记录 ${changeRecord.change_id}`);
    } catch (logError) {
      console.error('记录用户操作日志失败:', logError);
      // 日志记录失败不影响主要功能
    }

    res.json({
      code: 0,
      msg: deleteResult.rows[0]
    });
  } catch (error) {
    console.error('删除变更管理记录失败:', error);
    res.status(500).json({ code: 1, msg: error.message });
  }
};

module.exports = {
  getChangeManagementList,
  getSystemList,
  getUserList,
  addChangeManagement,
  updateChangeManagement,
  deleteChangeManagement,
  uploadFile,
  uploadFileSimple,
  downloadFile,
  removeFile,
  generateChangeId
};
