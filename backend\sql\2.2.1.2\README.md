# 版本 2.2.1.2 数据库更新说明

本目录包含版本 2.2.1.2 的数据库更新脚本。

## 更新内容

### 1. 交易日历和变更管理操作日志记录 (update_user_logs_view_for_calendar.sql)

该脚本更新了用户操作日志视图，为交易日历的值班排班操作和变更管理操作添加了专门的日志记录和显示功能：

- **功能增强**：
  - 为交易日历值班排班操作添加详细的操作日志记录
  - 为变更管理操作（新增、更新、删除）添加详细的操作日志记录
  - 在用户日志视图中特殊处理交易日历和变更管理操作，显示更友好的日志信息
  - 记录具体的日期和值班人员变更信息
  - 记录变更记录的编号、标题等关键信息

- **日志格式**：
  - 交易日历：`用户名更新了YYYY年MM月DD日的值班排班：主班: xxx, 副班: xxx, 值班经理: xxx, 仿真: xxx, 巡检: xxx`
  - 变更管理：`用户名新增了变更记录 [CHG202501010001] 系统升级变更`
  - 如果清空所有值班人员，显示：`用户名更新了YYYY年MM月DD日的值班排班：清空所有值班人员`

- **技术实现**：
  - 在后端控制器中添加专门的日志记录逻辑
  - 更新 `v_cmdb_user_logs` 视图以支持交易日历和变更管理操作的特殊显示
  - 为变更管理添加完整的CRUD操作日志记录
  - 修复"未知数据"显示问题，为无法匹配表注释的操作提供友好的描述
  - 保持与现有日志系统的兼容性

## 应用方法

### 方法一：使用psql命令行工具

```bash
# 连接到数据库
psql -U <用户名> -d <数据库名> -h <主机地址> -p <端口>

# 执行SQL脚本
\i update_user_logs_view_for_calendar.sql
```

### 方法二：使用pgAdmin或其他数据库管理工具

1. 打开pgAdmin或其他PostgreSQL数据库管理工具
2. 连接到数据库
3. 打开查询工具
4. 加载并执行 `update_user_logs_view_for_calendar.sql` 脚本

## 注意事项

- 脚本包含幂等性检查，可以多次执行而不会产生重复数据
- 更新后，交易日历的值班排班操作将在仪表盘的"最近活动"中显示详细信息
- 该更新不会影响现有的日志记录功能
- 如果您使用的是非admin用户，需要确保该用户有执行DDL操作的权限

## 验证更新

更新完成后，可以通过以下方式验证：

1. 在交易日历页面进行值班排班操作
2. 在变更管理页面进行新增、更新、删除操作
3. 查看仪表盘的"最近活动"区域
4. 确认显示了详细的值班排班变更信息和变更管理操作信息

## 回滚方法

如果需要回滚到原来的视图定义，可以执行：

```sql
-- 恢复原始视图定义（不包含交易日历特殊处理）
DROP VIEW IF EXISTS "public"."v_cmdb_user_logs";
CREATE VIEW "public"."v_cmdb_user_logs" AS  
SELECT ((t.username::text ||
        CASE
            WHEN t.operation_type::text = 'add'::text THEN '创建了新的'::text
            WHEN t.operation_type::text = 'update'::text THEN '更新了'::text
            WHEN t.operation_type::text = 'delete'::text THEN '删除了'::text
            ELSE '其它'::text
        END) || t2.table_comment) || '数据'::text AS activity_info,
    to_char(t."timestamp", 'yyyy-mm-dd hh24:mi:ss'::text) AS activity_time,
        CASE
            WHEN t.operation_type::text = 'add'::text THEN 'primary'::text
            WHEN t.operation_type::text = 'update'::text THEN 'success'::text
            WHEN t.operation_type::text = 'delete'::text THEN 'warning'::text
            ELSE 'primary'::text
        END AS activity_type
   FROM cmdb_user_logs t
     JOIN ( SELECT n.nspname,
            c.relname AS table_name,
            pgd.description AS table_comment
           FROM pg_class c
             JOIN pg_namespace n ON n.oid = c.relnamespace
             JOIN pg_description pgd ON pgd.objoid = c.oid AND pgd.objsubid = 0
          WHERE c.relkind = 'r'::"char" AND n.nspname = 'public'::name) t2 ON t2.table_name = replace(regexp_replace(t.url, '/api/(del_|update_|add_)'::text, ''::text, 'g'::text), '_password'::text, ''::text)
  ORDER BY t."timestamp" DESC, t.id
 LIMIT 5;
```
