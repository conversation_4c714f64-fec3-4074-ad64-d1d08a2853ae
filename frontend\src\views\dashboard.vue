<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="10">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>资产总数</span>
              <el-icon><Box /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.assetCount }}</div>
            <div class="compare">
              较上月
              <span :class="stats.assetGrowth >= 0 ? 'up' : 'down'">
                {{ Math.abs(stats.assetGrowth) }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>配置项</span>
              <el-icon><Setting /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.configCount }}</div>
            <div class="compare">
              较上月
              <span :class="stats.configGrowth >= 0 ? 'up' : 'down'">
                {{ Math.abs(stats.configGrowth) }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>变更数</span>
              <el-icon><RefreshRight /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.changeCount }}</div>
            <div class="compare">
              较上月
              <span :class="stats.changeGrowth >= 0 ? 'up' : 'down'">
                {{ Math.abs(stats.changeGrowth) }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>告警数</span>
              <el-icon><Warning /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.alertCount }}</div>
            <div class="compare">
              较上月
              <span :class="stats.alertGrowth >= 0 ? 'up' : 'down'">
                {{ Math.abs(stats.alertGrowth) }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <!-- <el-row :gutter="10" class="chart-row">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产分类统计</span>
            </div>
          </template>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>变更趋势</span>
            </div>
          </template>
        </el-card>
      </el-col>
    </el-row> -->

    <!-- 用户日志 -->
    <el-card shadow="hover" class="activity-card">
      <template #header>
        <div class="card-header">
          <span>用户日志</span>
          <div class="activity-count">共 {{ activities.length }} 条记录</div>
        </div>
      </template>
      <div class="activity-content">
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :timestamp="activity.activity_time"
            :type="activity.activity_type"
          >
            {{ activity.activity_info }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, getCurrentInstance } from "vue";
import axios from "axios"; // 如果你使用的是 axios，记得安装并导入它
import { ElMessage } from "element-plus";
import { Box, Setting, RefreshRight, Warning } from "@element-plus/icons-vue";

// 模拟数据
const stats = ref({
  assetCount: 1234,
  assetGrowth: 5.2,
  configCount: 456,
  configGrowth: -2.1,
  changeCount: 89,
  changeGrowth: 12.5,
  alertCount: 3,
  alertGrowth: -50,
});

const activities = ref([]);

// const loading = ref(false);
// 在组件挂载时调用 loadData 函数
const instance = getCurrentInstance();
const $axios = instance?.appContext.config.globalProperties.$axios;

onMounted(() => {
  loadData();
  loadData2();
});

async function loadData() {
  try {
    const response = await $axios.post(`/api/get_cmdb_dashboard`);
    stats.value.assetCount = response.data.msg[0].asset_count;
    stats.value.assetGrowth = response.data.msg[0].asset_growth;
    stats.value.configCount = response.data.msg[0].config_count;
    stats.value.configGrowth = response.data.msg[0].config_growth;

  } catch (error) {
    console.error("数据加载失败:", error);
    ElMessage.error("数据加载失败");
  }
}


async function loadData2() {
  try {
    const response = await $axios.post(`/api/get_cmdb_recent_activity`);
    activities.value = response.data.msg;

  } catch (error) {
    console.error("数据加载失败:", error);
    ElMessage.error("数据加载失败");
  }
}

</script>

<style lang="scss" scoped>
.dashboard {
  .stat-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-body {
      text-align: center;

      .number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .compare {
        color: #909399;
        font-size: 14px;

        .up {
          color: #67c23a;
        }

        .down {
          color: #f56c6c;
        }
      }
    }
  }

  .chart-row {
    margin-top: 20px;
  }

  .activity-card {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .activity-count {
        color: #909399;
        font-size: 14px;
      }
    }

    .activity-content {
      max-height: 400px; /* 设置最大高度 */
      overflow-y: auto; /* 启用垂直滚动 */
      padding-right: 8px; /* 为滚动条留出空间 */

      /* 自定义滚动条样式 - 与系统保持一致 */
      scrollbar-width: auto;
      scrollbar-color: rgba(144, 147, 153, 0.3) transparent;

      &::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.3);
        border-radius: 6px;
        border: 3px solid transparent;
        background-clip: padding-box;
        transition: background-color 0.3s;
      }

      &:hover::-webkit-scrollbar-thumb {
        background: rgba(144, 147, 153, 0.5);
      }

      &::-webkit-scrollbar-thumb:active {
        background: rgba(144, 147, 153, 0.7);
      }

      &::-webkit-scrollbar-corner {
        background: transparent;
      }
    }
  }
}
</style>