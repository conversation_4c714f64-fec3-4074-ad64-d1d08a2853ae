<template>
  <div class="static-test-container">
    <h2>静态资源加载测试页面</h2>
    
    <div class="test-section">
      <h3>当前页面信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>当前URL:</label>
          <span>{{ currentUrl }}</span>
        </div>
        <div class="info-item">
          <label>Base URL:</label>
          <span>{{ baseUrl }}</span>
        </div>
        <div class="info-item">
          <label>路由路径:</label>
          <span>{{ $route.path }}</span>
        </div>
        <div class="info-item">
          <label>路由名称:</label>
          <span>{{ $route.name }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>静态资源路径测试</h3>
      <div class="resource-tests">
        <div class="test-item">
          <h4>CSS文件检测</h4>
          <div class="resource-list">
            <div v-for="css in cssFiles" :key="css.href" class="resource-item">
              <span class="resource-path">{{ css.href }}</span>
              <span :class="['status', css.loaded ? 'loaded' : 'failed']">
                {{ css.loaded ? '✓ 已加载' : '✗ 加载失败' }}
              </span>
            </div>
          </div>
        </div>

        <div class="test-item">
          <h4>JS文件检测</h4>
          <div class="resource-list">
            <div v-for="js in jsFiles" :key="js.src" class="resource-item">
              <span class="resource-path">{{ js.src }}</span>
              <span :class="['status', js.loaded ? 'loaded' : 'failed']">
                {{ js.loaded ? '✓ 已加载' : '✗ 加载失败' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>图片加载测试</h3>
      <div class="image-tests">
        <div class="image-test-item">
          <h4>直接路径加载:</h4>
          <img src="/assets/logo.png" alt="Logo直接路径" @load="onImageLoad" @error="onImageError" />
          <p>路径: /assets/logo.png</p>
        </div>
        <div class="image-test-item">
          <h4>带路由前缀路径:</h4>
          <img src="/ops_change_management/assets/logo.png" alt="Logo路由前缀路径" @load="onImageLoad" @error="onImageError" />
          <p>路径: /ops_change_management/assets/logo.png</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>网络请求测试</h3>
      <el-button @click="testStaticResource" type="primary">测试静态资源访问</el-button>
      <el-button @click="testApiAccess" type="success">测试API访问</el-button>

      <div class="test-results" v-if="testResults.length > 0">
        <h4>测试结果</h4>
        <div v-for="(result, index) in testResults" :key="index" class="test-result">
          <span class="test-url">{{ result.url }}</span>
          <span :class="['test-status', result.success ? 'success' : 'error']">
            {{ result.success ? '✓' : '✗' }} {{ result.status }}
          </span>
          <span class="test-message">{{ result.message }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>解决方案建议</h3>
      <div class="solutions">
        <div class="solution-item">
          <h4>如果看到404错误：</h4>
          <ol>
            <li>检查nginx配置是否包含 <code>location /assets/</code> 规则</li>
            <li>确认静态文件目录路径正确</li>
            <li>重载nginx配置：<code>nginx -s reload</code></li>
          </ol>
        </div>
        
        <div class="solution-item">
          <h4>nginx配置示例：</h4>
          <pre><code># 处理assets目录下的静态资源 - 优先级最高
location /assets/ {
    root /home/<USER>/frontend/dist;
    expires max;
    add_header Cache-Control "public, max-age=31536000, immutable";
}

# 处理带路由前缀的assets请求 - 重写到正确的assets路径
location ~ ^/[^/]+/assets/(.*)$ {
    rewrite ^/[^/]+/assets/(.*)$ /assets/$1 last;
}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StaticResourceTest',
  data() {
    return {
      currentUrl: window.location.href,
      baseUrl: window.location.origin,
      cssFiles: [],
      jsFiles: [],
      testResults: []
    }
  },
  mounted() {
    this.detectLoadedResources();
  },
  methods: {
    detectLoadedResources() {
      // 检测CSS文件
      const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
      this.cssFiles = Array.from(cssLinks).map(link => ({
        href: link.href,
        loaded: link.sheet !== null
      }));

      // 检测JS文件
      const jsScripts = document.querySelectorAll('script[src]');
      this.jsFiles = Array.from(jsScripts).map(script => ({
        src: script.src,
        loaded: script.readyState === 'complete' || script.readyState === 'loaded'
      }));
    },

    async testStaticResource() {
      this.testResults = [];

      // 测试常见的静态资源路径
      const testUrls = [
        '/assets/logo.png',  // 测试实际的logo文件
        '/ops_change_management/assets/logo.png',  // 测试带路由前缀的路径
        '/assets/index.js',
        '/assets/index.css',
        `${this.baseUrl}/assets/logo.png`,
        `${this.baseUrl}/assets/index.js`,
        `${this.baseUrl}/assets/index.css`
      ];

      for (const url of testUrls) {
        try {
          const response = await fetch(url, { method: 'HEAD' });
          this.testResults.push({
            url: url,
            success: response.ok,
            status: response.status,
            message: response.ok ? '资源可访问' : `HTTP ${response.status}`
          });
        } catch (error) {
          this.testResults.push({
            url: url,
            success: false,
            status: 'ERROR',
            message: error.message
          });
        }
      }
    },

    async testApiAccess() {
      try {
        const response = await this.$axios.post('/api/test_method', {
          test: 'static_resource_page'
        });

        this.testResults.push({
          url: '/api/test_method',
          success: true,
          status: 200,
          message: 'API访问正常'
        });
      } catch (error) {
        this.testResults.push({
          url: '/api/test_method',
          success: false,
          status: error.response?.status || 'ERROR',
          message: error.message
        });
      }
    },

    onImageLoad(event) {
      console.log('图片加载成功:', event.target.src);
      this.$message.success(`图片加载成功: ${event.target.alt}`);
    },

    onImageError(event) {
      console.error('图片加载失败:', event.target.src);
      this.$message.error(`图片加载失败: ${event.target.alt}`);
    }
  }
}
</script>

<style scoped>
.static-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item label {
  font-weight: bold;
  min-width: 80px;
}

.resource-tests {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.test-item h4 {
  margin-bottom: 10px;
  color: #333;
}

.resource-list {
  max-height: 200px;
  overflow-y: auto;
}

.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.resource-path {
  font-family: monospace;
  font-size: 12px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status {
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.loaded {
  background: #e7f5e7;
  color: #52c41a;
}

.status.failed {
  background: #ffe7e7;
  color: #ff4d4f;
}

.test-results {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 4px;
}

.test-result {
  display: grid;
  grid-template-columns: 2fr auto 1fr;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.test-url {
  font-family: monospace;
  font-size: 12px;
}

.test-status.success {
  color: #52c41a;
  font-weight: bold;
}

.test-status.error {
  color: #ff4d4f;
  font-weight: bold;
}

.test-message {
  font-size: 12px;
  color: #666;
}

.solutions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.solution-item h4 {
  color: #1890ff;
  margin-bottom: 10px;
}

.solution-item ol {
  padding-left: 20px;
}

.solution-item pre {
  background: #f6f8fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.solution-item code {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: monospace;
}

.image-tests {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.image-test-item {
  text-align: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.image-test-item h4 {
  margin-bottom: 10px;
  color: #333;
}

.image-test-item img {
  max-width: 100px;
  max-height: 100px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
}

.image-test-item p {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  margin: 0;
}
</style>
