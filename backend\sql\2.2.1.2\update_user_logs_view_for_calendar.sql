-- 更新用户日志视图以支持交易日历和变更管理操作日志
-- 版本: 2.2.1.2
-- 日期: 2025年6月3日
-- 说明: 为交易日历的值班排班操作和变更管理操作添加专门的日志显示逻辑

-- 删除现有视图
DROP VIEW IF EXISTS "public"."v_cmdb_user_logs";

-- 重新创建视图，添加对交易日历和变更管理操作的特殊处理
CREATE VIEW "public"."v_cmdb_user_logs" AS
SELECT
    CASE
        -- 特殊处理交易日历值班排班操作
        WHEN t.url = '/api/update_ops_calendar_duty' AND t.body::jsonb ? 'action' THEN
            t.username::text || '更新了' || (t.body::jsonb->>'date') || '的值班排班：' || (t.body::jsonb->>'duty_info')
        -- 特殊处理变更管理操作
        WHEN t.url IN ('/api/add_ops_change_management', '/api/update_ops_change_management', '/api/del_ops_change_management')
             AND t.body::jsonb ? 'action' THEN
            t.username::text ||
            CASE
                WHEN t.body::jsonb->>'action' = '新增变更记录' THEN '新增了变更记录'
                WHEN t.body::jsonb->>'action' = '更新变更记录' THEN '更新了变更记录'
                WHEN t.body::jsonb->>'action' = '删除变更记录' THEN '删除了变更记录'
                ELSE '操作了变更记录'
            END ||
            ' [' || (t.body::jsonb->>'change_id') || '] ' ||
            COALESCE((t.body::jsonb->>'title'), '未知标题')
        -- 其他操作使用原有逻辑
        ELSE
            CASE
                -- 如果能匹配到表注释，使用表注释
                WHEN t2.table_comment IS NOT NULL THEN
                    ((t.username::text ||
                        CASE
                            WHEN t.operation_type::text = 'add'::text THEN '创建了新的'::text
                            WHEN t.operation_type::text = 'update'::text THEN '更新了'::text
                            WHEN t.operation_type::text = 'delete'::text THEN '删除了'::text
                            ELSE '操作了'::text
                        END) || t2.table_comment) || '数据'::text
                -- 如果无法匹配到表注释，尝试从URL中提取友好的操作描述
                ELSE
                    t.username::text ||
                    CASE
                        WHEN t.url LIKE '%login%' THEN '进行了登录操作'
                        WHEN t.url LIKE '%logout%' THEN '进行了登出操作'
                        WHEN t.url LIKE '%get_%' THEN '查询了数据'
                        WHEN t.url LIKE '%upload%' THEN '上传了文件'
                        WHEN t.url LIKE '%download%' THEN '下载了文件'
                        WHEN t.url LIKE '%remove%' THEN '删除了文件'
                        WHEN t.url LIKE '%template%' THEN '操作了模板'
                        WHEN t.url LIKE '%permission%' THEN '操作了权限'
                        WHEN t.url LIKE '%discovery%' THEN '操作了自动发现'
                        WHEN t.operation_type::text = 'add'::text THEN '创建了数据'
                        WHEN t.operation_type::text = 'update'::text THEN '更新了数据'
                        WHEN t.operation_type::text = 'delete'::text THEN '删除了数据'
                        ELSE '进行了系统操作'
                    END
            END
    END AS activity_info,
    to_char(t."timestamp", 'yyyy-mm-dd hh24:mi:ss'::text) AS activity_time,
    CASE
        WHEN t.operation_type::text = 'add'::text THEN 'primary'::text
        WHEN t.operation_type::text = 'update'::text THEN 'success'::text
        WHEN t.operation_type::text = 'delete'::text THEN 'warning'::text
        ELSE 'primary'::text
    END AS activity_type
FROM cmdb_user_logs t
LEFT JOIN (
    SELECT n.nspname,
        c.relname AS table_name,
        pgd.description AS table_comment
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    JOIN pg_description pgd ON pgd.objoid = c.oid AND pgd.objsubid = 0
    WHERE c.relkind = 'r'::"char" AND n.nspname = 'public'::name
) t2 ON t2.table_name = replace(regexp_replace(t.url, '/api/(del_|update_|add_)'::text, ''::text, 'g'::text), '_password'::text, ''::text)
ORDER BY t."timestamp" DESC, t.id
LIMIT 50;

-- 添加视图注释
COMMENT ON VIEW "public"."v_cmdb_user_logs" IS '用户操作日志视图，支持交易日历值班排班操作和变更管理操作的特殊显示';
